// THE AUTOGENERATED LICENSE. ALL THE RIGHTS ARE RESERVED BY ROBOTS.

// WARNING: This file has automatically been generated on Sat, 30 Aug 2025 00:53:09 +03.
// Code generated by https://git.io/c-for-go. DO NOT EDIT.

package gtk4go

/*
#cgo CFLAGS: pkg-config gtk4
#cgo LDFLAGS: pkg-config gtk4
#include <gtk/gtk.h>
#include <stdlib.h>
#include "cgo_helpers.h"
*/
import "C"

const (
// Gtk4SimpleH as defined in gtk4go/gtk4_simple.h:2

)

// Orientation as declared in gtk4go/gtk4_simple.h:41
type Orientation int32

// Orientation enumeration from gtk4go/gtk4_simple.h:41
const (
	OrientationHorizontal Orientation = iota
	OrientationVertical   Orientation = 1
)

// Align as declared in gtk4go/gtk4_simple.h:49
type Align int32

// Align enumeration from gtk4go/gtk4_simple.h:49
const (
	AlignFill     Align = iota
	AlignStart    Align = 1
	AlignEnd      Align = 2
	AlignCenter   Align = 3
	AlignBaseline Align = 4
)
