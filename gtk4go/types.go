// THE AUTOGENERATED LICENSE. ALL THE RIGHTS ARE RESERVED BY ROBOTS.

// WARNING: This file has automatically been generated on Sat, 30 Aug 2025 00:53:09 +03.
// Code generated by https://git.io/c-for-go. DO NOT EDIT.

package gtk4go

/*
#cgo CFLAGS: pkg-config gtk4
#cgo LDFLAGS: pkg-config gtk4
#include <gtk/gtk.h>
#include <stdlib.h>
#include "cgo_helpers.h"
*/
import "C"

// Widget as declared in gtk4go/gtk4_simple.h:20
type Widget C.GtkWidget

// Window as declared in gtk4go/gtk4_simple.h:21
type Window C.GtkWindow

// Application as declared in gtk4go/gtk4_simple.h:22
type Application C.GtkApplication

// Button as declared in gtk4go/gtk4_simple.h:23
type Button C.GtkButton

// Label as declared in gtk4go/gtk4_simple.h:24
type Label C.GtkLabel

// Box as declared in gtk4go/gtk4_simple.h:25
type Box C.GtkBox

// GdkDisplay as declared in gtk4go/gtk4_simple.h:28
type GdkDisplay C.GdkDisplay

// GdkSurface as declared in gtk4go/gtk4_simple.h:29
type GdkSurface C.GdkSurface

// GdkDevice as declared in gtk4go/gtk4_simple.h:30
type GdkDevice C.GdkDevice

// GdkEvent as declared in gtk4go/gtk4_simple.h:31
type GdkEvent C.GdkEvent

// GskRenderer as declared in gtk4go/gtk4_simple.h:34
type GskRenderer C.GskRenderer

// GskRenderNode as declared in gtk4go/gtk4_simple.h:35
type GskRenderNode C.GskRenderNode

// GCallback type as declared in gtk4go/gtk4_simple.h:67
type GCallback func()
