// THE AUTOGENERATED LICENSE. ALL THE RIGHTS ARE RESERVED BY ROBOTS.

// WARNING: This file has automatically been generated on Sat, 30 Aug 2025 00:53:09 +03.
// Code generated by https://git.io/c-for-go. DO NOT EDIT.

package gtk4go

/*
#cgo CFLAGS: pkg-config gtk4
#cgo LDFLAGS: pkg-config gtk4
#include <gtk/gtk.h>
#include <stdlib.h>
#include "cgo_helpers.h"
*/
import "C"
import (
	"fmt"
	"runtime"
	"sync"
	"unsafe"
)

// Ref returns a reference to C object as it is.
func (x *Widget) Ref() *C.GtkWidget {
	if x == nil {
		return nil
	}
	return (*C.GtkWidget)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *Widget) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewWidgetRef converts the C object reference into a raw struct reference without wrapping.
func NewWidgetRef(ref unsafe.Pointer) *Widget {
	return (*Widget)(ref)
}

// NewWidget allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewWidget() *Widget {
	return (*Widget)(allocWidgetMemory(1))
}

// allocWidgetMemory allocates memory for type C.GtkWidget in C.
// The caller is responsible for freeing the this memory via C.free.
func allocWidgetMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfWidgetValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfWidgetValue = unsafe.Sizeof([1]C.GtkWidget{})

// cgoAllocMap stores pointers to C allocated memory for future reference.
type cgoAllocMap struct {
	mux sync.RWMutex
	m   map[unsafe.Pointer]struct{}
}

var cgoAllocsUnknown = new(cgoAllocMap)

func (a *cgoAllocMap) Add(ptr unsafe.Pointer) {
	a.mux.Lock()
	if a.m == nil {
		a.m = make(map[unsafe.Pointer]struct{})
	}
	a.m[ptr] = struct{}{}
	a.mux.Unlock()
}

func (a *cgoAllocMap) IsEmpty() bool {
	a.mux.RLock()
	isEmpty := len(a.m) == 0
	a.mux.RUnlock()
	return isEmpty
}

func (a *cgoAllocMap) Borrow(b *cgoAllocMap) {
	if b == nil || b.IsEmpty() {
		return
	}
	b.mux.Lock()
	a.mux.Lock()
	for ptr := range b.m {
		if a.m == nil {
			a.m = make(map[unsafe.Pointer]struct{})
		}
		a.m[ptr] = struct{}{}
		delete(b.m, ptr)
	}
	a.mux.Unlock()
	b.mux.Unlock()
}

func (a *cgoAllocMap) Free() {
	a.mux.Lock()
	for ptr := range a.m {
		C.free(ptr)
		delete(a.m, ptr)
	}
	a.mux.Unlock()
}

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *Widget) PassRef() *C.GtkWidget {
	if x == nil {
		x = (*Widget)(allocWidgetMemory(1))
	}
	return (*C.GtkWidget)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *Window) Ref() *C.GtkWindow {
	if x == nil {
		return nil
	}
	return (*C.GtkWindow)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *Window) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewWindowRef converts the C object reference into a raw struct reference without wrapping.
func NewWindowRef(ref unsafe.Pointer) *Window {
	return (*Window)(ref)
}

// NewWindow allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewWindow() *Window {
	return (*Window)(allocWindowMemory(1))
}

// allocWindowMemory allocates memory for type C.GtkWindow in C.
// The caller is responsible for freeing the this memory via C.free.
func allocWindowMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfWindowValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfWindowValue = unsafe.Sizeof([1]C.GtkWindow{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *Window) PassRef() *C.GtkWindow {
	if x == nil {
		x = (*Window)(allocWindowMemory(1))
	}
	return (*C.GtkWindow)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *Application) Ref() *C.GtkApplication {
	if x == nil {
		return nil
	}
	return (*C.GtkApplication)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *Application) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewApplicationRef converts the C object reference into a raw struct reference without wrapping.
func NewApplicationRef(ref unsafe.Pointer) *Application {
	return (*Application)(ref)
}

// NewApplication allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewApplication() *Application {
	return (*Application)(allocApplicationMemory(1))
}

// allocApplicationMemory allocates memory for type C.GtkApplication in C.
// The caller is responsible for freeing the this memory via C.free.
func allocApplicationMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfApplicationValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfApplicationValue = unsafe.Sizeof([1]C.GtkApplication{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *Application) PassRef() *C.GtkApplication {
	if x == nil {
		x = (*Application)(allocApplicationMemory(1))
	}
	return (*C.GtkApplication)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *Button) Ref() *C.GtkButton {
	if x == nil {
		return nil
	}
	return (*C.GtkButton)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *Button) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewButtonRef converts the C object reference into a raw struct reference without wrapping.
func NewButtonRef(ref unsafe.Pointer) *Button {
	return (*Button)(ref)
}

// NewButton allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewButton() *Button {
	return (*Button)(allocButtonMemory(1))
}

// allocButtonMemory allocates memory for type C.GtkButton in C.
// The caller is responsible for freeing the this memory via C.free.
func allocButtonMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfButtonValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfButtonValue = unsafe.Sizeof([1]C.GtkButton{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *Button) PassRef() *C.GtkButton {
	if x == nil {
		x = (*Button)(allocButtonMemory(1))
	}
	return (*C.GtkButton)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *Label) Ref() *C.GtkLabel {
	if x == nil {
		return nil
	}
	return (*C.GtkLabel)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *Label) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewLabelRef converts the C object reference into a raw struct reference without wrapping.
func NewLabelRef(ref unsafe.Pointer) *Label {
	return (*Label)(ref)
}

// NewLabel allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewLabel() *Label {
	return (*Label)(allocLabelMemory(1))
}

// allocLabelMemory allocates memory for type C.GtkLabel in C.
// The caller is responsible for freeing the this memory via C.free.
func allocLabelMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfLabelValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfLabelValue = unsafe.Sizeof([1]C.GtkLabel{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *Label) PassRef() *C.GtkLabel {
	if x == nil {
		x = (*Label)(allocLabelMemory(1))
	}
	return (*C.GtkLabel)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *Box) Ref() *C.GtkBox {
	if x == nil {
		return nil
	}
	return (*C.GtkBox)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *Box) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewBoxRef converts the C object reference into a raw struct reference without wrapping.
func NewBoxRef(ref unsafe.Pointer) *Box {
	return (*Box)(ref)
}

// NewBox allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewBox() *Box {
	return (*Box)(allocBoxMemory(1))
}

// allocBoxMemory allocates memory for type C.GtkBox in C.
// The caller is responsible for freeing the this memory via C.free.
func allocBoxMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfBoxValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfBoxValue = unsafe.Sizeof([1]C.GtkBox{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *Box) PassRef() *C.GtkBox {
	if x == nil {
		x = (*Box)(allocBoxMemory(1))
	}
	return (*C.GtkBox)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *GdkDisplay) Ref() *C.GdkDisplay {
	if x == nil {
		return nil
	}
	return (*C.GdkDisplay)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *GdkDisplay) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewGdkDisplayRef converts the C object reference into a raw struct reference without wrapping.
func NewGdkDisplayRef(ref unsafe.Pointer) *GdkDisplay {
	return (*GdkDisplay)(ref)
}

// NewGdkDisplay allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewGdkDisplay() *GdkDisplay {
	return (*GdkDisplay)(allocGdkDisplayMemory(1))
}

// allocGdkDisplayMemory allocates memory for type C.GdkDisplay in C.
// The caller is responsible for freeing the this memory via C.free.
func allocGdkDisplayMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfGdkDisplayValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfGdkDisplayValue = unsafe.Sizeof([1]C.GdkDisplay{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *GdkDisplay) PassRef() *C.GdkDisplay {
	if x == nil {
		x = (*GdkDisplay)(allocGdkDisplayMemory(1))
	}
	return (*C.GdkDisplay)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *GdkSurface) Ref() *C.GdkSurface {
	if x == nil {
		return nil
	}
	return (*C.GdkSurface)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *GdkSurface) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewGdkSurfaceRef converts the C object reference into a raw struct reference without wrapping.
func NewGdkSurfaceRef(ref unsafe.Pointer) *GdkSurface {
	return (*GdkSurface)(ref)
}

// NewGdkSurface allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewGdkSurface() *GdkSurface {
	return (*GdkSurface)(allocGdkSurfaceMemory(1))
}

// allocGdkSurfaceMemory allocates memory for type C.GdkSurface in C.
// The caller is responsible for freeing the this memory via C.free.
func allocGdkSurfaceMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfGdkSurfaceValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfGdkSurfaceValue = unsafe.Sizeof([1]C.GdkSurface{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *GdkSurface) PassRef() *C.GdkSurface {
	if x == nil {
		x = (*GdkSurface)(allocGdkSurfaceMemory(1))
	}
	return (*C.GdkSurface)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *GdkDevice) Ref() *C.GdkDevice {
	if x == nil {
		return nil
	}
	return (*C.GdkDevice)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *GdkDevice) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewGdkDeviceRef converts the C object reference into a raw struct reference without wrapping.
func NewGdkDeviceRef(ref unsafe.Pointer) *GdkDevice {
	return (*GdkDevice)(ref)
}

// NewGdkDevice allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewGdkDevice() *GdkDevice {
	return (*GdkDevice)(allocGdkDeviceMemory(1))
}

// allocGdkDeviceMemory allocates memory for type C.GdkDevice in C.
// The caller is responsible for freeing the this memory via C.free.
func allocGdkDeviceMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfGdkDeviceValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfGdkDeviceValue = unsafe.Sizeof([1]C.GdkDevice{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *GdkDevice) PassRef() *C.GdkDevice {
	if x == nil {
		x = (*GdkDevice)(allocGdkDeviceMemory(1))
	}
	return (*C.GdkDevice)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *GdkEvent) Ref() *C.GdkEvent {
	if x == nil {
		return nil
	}
	return (*C.GdkEvent)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *GdkEvent) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewGdkEventRef converts the C object reference into a raw struct reference without wrapping.
func NewGdkEventRef(ref unsafe.Pointer) *GdkEvent {
	return (*GdkEvent)(ref)
}

// NewGdkEvent allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewGdkEvent() *GdkEvent {
	return (*GdkEvent)(allocGdkEventMemory(1))
}

// allocGdkEventMemory allocates memory for type C.GdkEvent in C.
// The caller is responsible for freeing the this memory via C.free.
func allocGdkEventMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfGdkEventValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfGdkEventValue = unsafe.Sizeof([1]C.GdkEvent{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *GdkEvent) PassRef() *C.GdkEvent {
	if x == nil {
		x = (*GdkEvent)(allocGdkEventMemory(1))
	}
	return (*C.GdkEvent)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *GskRenderer) Ref() *C.GskRenderer {
	if x == nil {
		return nil
	}
	return (*C.GskRenderer)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *GskRenderer) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewGskRendererRef converts the C object reference into a raw struct reference without wrapping.
func NewGskRendererRef(ref unsafe.Pointer) *GskRenderer {
	return (*GskRenderer)(ref)
}

// NewGskRenderer allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewGskRenderer() *GskRenderer {
	return (*GskRenderer)(allocGskRendererMemory(1))
}

// allocGskRendererMemory allocates memory for type C.GskRenderer in C.
// The caller is responsible for freeing the this memory via C.free.
func allocGskRendererMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfGskRendererValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfGskRendererValue = unsafe.Sizeof([1]C.GskRenderer{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *GskRenderer) PassRef() *C.GskRenderer {
	if x == nil {
		x = (*GskRenderer)(allocGskRendererMemory(1))
	}
	return (*C.GskRenderer)(unsafe.Pointer(x))
}

// Ref returns a reference to C object as it is.
func (x *GskRenderNode) Ref() *C.GskRenderNode {
	if x == nil {
		return nil
	}
	return (*C.GskRenderNode)(unsafe.Pointer(x))
}

// Free cleanups the referenced memory using C free.
func (x *GskRenderNode) Free() {
	if x != nil {
		C.free(unsafe.Pointer(x))
	}
}

// NewGskRenderNodeRef converts the C object reference into a raw struct reference without wrapping.
func NewGskRenderNodeRef(ref unsafe.Pointer) *GskRenderNode {
	return (*GskRenderNode)(ref)
}

// NewGskRenderNode allocates a new C object of this type and converts the reference into
// a raw struct reference without wrapping.
func NewGskRenderNode() *GskRenderNode {
	return (*GskRenderNode)(allocGskRenderNodeMemory(1))
}

// allocGskRenderNodeMemory allocates memory for type C.GskRenderNode in C.
// The caller is responsible for freeing the this memory via C.free.
func allocGskRenderNodeMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfGskRenderNodeValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfGskRenderNodeValue = unsafe.Sizeof([1]C.GskRenderNode{})

// PassRef returns a reference to C object as it is or allocates a new C object of this type.
func (x *GskRenderNode) PassRef() *C.GskRenderNode {
	if x == nil {
		x = (*GskRenderNode)(allocGskRenderNodeMemory(1))
	}
	return (*C.GskRenderNode)(unsafe.Pointer(x))
}

func (x GCallback) PassRef() (ref *C.GCallback, allocs *cgoAllocMap) {
	if x == nil {
		return nil, nil
	}
	if gCallbackB957B379Func == nil {
		gCallbackB957B379Func = x
	}
	return (*C.GCallback)(C.GCallback_b957b379), nil
}

func (x GCallback) PassValue() (ref C.GCallback, allocs *cgoAllocMap) {
	if x == nil {
		return nil, nil
	}
	if gCallbackB957B379Func == nil {
		gCallbackB957B379Func = x
	}
	return (C.GCallback)(C.GCallback_b957b379), nil
}

func NewGCallbackRef(ref unsafe.Pointer) *GCallback {
	return (*GCallback)(ref)
}

//export gCallbackB957B379
func gCallbackB957B379() {
	if gCallbackB957B379Func != nil {
		gCallbackB957B379Func()
		return
	}
	panic("callback func has not been set (race?)")
}

var gCallbackB957B379Func GCallback

// unpackPGcharString copies the data from Go string as *C.gchar.
func unpackPGcharString(str string) (*C.gchar, *cgoAllocMap) {
	allocs := new(cgoAllocMap)
	defer runtime.SetFinalizer(allocs, func(a *cgoAllocMap) {
		go a.Free()
	})

	mem0 := unsafe.Pointer(C.CString(str))
	allocs.Add(mem0)
	return (*C.gchar)(mem0), allocs
}

type stringHeader struct {
	Data unsafe.Pointer
	Len  int
}

type sliceHeader struct {
	Data unsafe.Pointer
	Len  int
	Cap  int
}

// copyPCharBytes copies the data from Go slice as *C.char.
func copyPCharBytes(slice *sliceHeader) (*C.char, *cgoAllocMap) {
	allocs := new(cgoAllocMap)
	defer runtime.SetFinalizer(allocs, func(a *cgoAllocMap) {
		go a.Free()
	})

	mem0 := unsafe.Pointer(C.CBytes(*(*[]byte)(unsafe.Pointer(&sliceHeader{
		Data: slice.Data,
		Len:  int(sizeOfCharValue) * slice.Len,
		Cap:  int(sizeOfCharValue) * slice.Len,
	}))))
	allocs.Add(mem0)

	return (*C.char)(mem0), allocs
}

// allocCharMemory allocates memory for type C.char in C.
// The caller is responsible for freeing the this memory via C.free.
func allocCharMemory(n int) unsafe.Pointer {
	mem, err := C.calloc(C.size_t(n), (C.size_t)(sizeOfCharValue))
	if mem == nil {
		panic(fmt.Sprintln("memory alloc error: ", err))
	}
	return mem
}

const sizeOfCharValue = unsafe.Sizeof([1]C.char{})
