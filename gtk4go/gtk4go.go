// THE AUTOGENERATED LICENSE. ALL THE RIGHTS ARE RESERVED BY ROBOTS.

// WARNING: This file has automatically been generated on Sat, 30 Aug 2025 00:53:09 +03.
// Code generated by https://git.io/c-for-go. DO NOT EDIT.

package gtk4go

/*
#cgo CFLAGS: pkg-config gtk4
#cgo LDFLAGS: pkg-config gtk4
#include <gtk/gtk.h>
#include <stdlib.h>
#include "cgo_helpers.h"
*/
import "C"
import (
	"runtime"
	"unsafe"
)

// ApplicationNew function as declared in gtk4go/gtk4_simple.h:52
func ApplicationNew(applicationId string, flags int32) *Application {
	capplicationId, capplicationIdAllocMap := unpackPGcharString(applicationId)
	cflags, cflagsAllocMap := (C.int)(flags), cgoAllocsUnknown
	__ret := C.gtk_application_new(capplicationId, cflags)
	runtime.KeepAlive(cflagsAllocMap)
	runtime.KeepAlive(capplicationIdAllocMap)
	__v := *(**Application)(unsafe.Pointer(&__ret))
	return __v
}

// ApplicationRun function as declared in gtk4go/gtk4_simple.h:53
func ApplicationRun(app *Application, argc int32, argv []*byte) int32 {
	capp, cappAllocMap := (*C.GtkApplication)(unsafe.Pointer(app)), cgoAllocsUnknown
	cargc, cargcAllocMap := (C.int)(argc), cgoAllocsUnknown
	cargv, cargvAllocMap := (**C.char)(unsafe.Pointer((*sliceHeader)(unsafe.Pointer(&argv)).Data)), cgoAllocsUnknown
	__ret := C.gtk_application_run(capp, cargc, cargv)
	runtime.KeepAlive(cargvAllocMap)
	runtime.KeepAlive(cargcAllocMap)
	runtime.KeepAlive(cappAllocMap)
	__v := (int32)(__ret)
	return __v
}

// ApplicationWindowNew function as declared in gtk4go/gtk4_simple.h:54
func ApplicationWindowNew(app *Application) *Widget {
	capp, cappAllocMap := (*C.GtkApplication)(unsafe.Pointer(app)), cgoAllocsUnknown
	__ret := C.gtk_application_window_new(capp)
	runtime.KeepAlive(cappAllocMap)
	__v := *(**Widget)(unsafe.Pointer(&__ret))
	return __v
}

// WindowNew function as declared in gtk4go/gtk4_simple.h:55
func WindowNew() *Widget {
	__ret := C.gtk_window_new()
	__v := *(**Widget)(unsafe.Pointer(&__ret))
	return __v
}

// WindowSetTitle function as declared in gtk4go/gtk4_simple.h:56
func WindowSetTitle(window *Window, title string) {
	cwindow, cwindowAllocMap := (*C.GtkWindow)(unsafe.Pointer(window)), cgoAllocsUnknown
	ctitle, ctitleAllocMap := unpackPGcharString(title)
	C.gtk_window_set_title(cwindow, ctitle)
	runtime.KeepAlive(ctitleAllocMap)
	runtime.KeepAlive(cwindowAllocMap)
}

// WindowSetDefaultSize function as declared in gtk4go/gtk4_simple.h:57
func WindowSetDefaultSize(window *Window, width int32, height int32) {
	cwindow, cwindowAllocMap := (*C.GtkWindow)(unsafe.Pointer(window)), cgoAllocsUnknown
	cwidth, cwidthAllocMap := (C.gint)(width), cgoAllocsUnknown
	cheight, cheightAllocMap := (C.gint)(height), cgoAllocsUnknown
	C.gtk_window_set_default_size(cwindow, cwidth, cheight)
	runtime.KeepAlive(cheightAllocMap)
	runtime.KeepAlive(cwidthAllocMap)
	runtime.KeepAlive(cwindowAllocMap)
}

// WindowPresent function as declared in gtk4go/gtk4_simple.h:58
func WindowPresent(window *Window) {
	cwindow, cwindowAllocMap := (*C.GtkWindow)(unsafe.Pointer(window)), cgoAllocsUnknown
	C.gtk_window_present(cwindow)
	runtime.KeepAlive(cwindowAllocMap)
}

// ButtonNewWithLabel function as declared in gtk4go/gtk4_simple.h:59
func ButtonNewWithLabel(label string) *Widget {
	clabel, clabelAllocMap := unpackPGcharString(label)
	__ret := C.gtk_button_new_with_label(clabel)
	runtime.KeepAlive(clabelAllocMap)
	__v := *(**Widget)(unsafe.Pointer(&__ret))
	return __v
}

// LabelNew function as declared in gtk4go/gtk4_simple.h:60
func LabelNew(str string) *Widget {
	cstr, cstrAllocMap := unpackPGcharString(str)
	__ret := C.gtk_label_new(cstr)
	runtime.KeepAlive(cstrAllocMap)
	__v := *(**Widget)(unsafe.Pointer(&__ret))
	return __v
}

// BoxNew function as declared in gtk4go/gtk4_simple.h:61
func BoxNew(orientation Orientation, spacing int32) *Widget {
	corientation, corientationAllocMap := (C.GtkOrientation)(orientation), cgoAllocsUnknown
	cspacing, cspacingAllocMap := (C.gint)(spacing), cgoAllocsUnknown
	__ret := C.gtk_box_new(corientation, cspacing)
	runtime.KeepAlive(cspacingAllocMap)
	runtime.KeepAlive(corientationAllocMap)
	__v := *(**Widget)(unsafe.Pointer(&__ret))
	return __v
}

// BoxAppend function as declared in gtk4go/gtk4_simple.h:62
func BoxAppend(box *Box, child *Widget) {
	cbox, cboxAllocMap := (*C.GtkBox)(unsafe.Pointer(box)), cgoAllocsUnknown
	cchild, cchildAllocMap := (*C.GtkWidget)(unsafe.Pointer(child)), cgoAllocsUnknown
	C.gtk_box_append(cbox, cchild)
	runtime.KeepAlive(cchildAllocMap)
	runtime.KeepAlive(cboxAllocMap)
}

// WindowSetChild function as declared in gtk4go/gtk4_simple.h:63
func WindowSetChild(window *Window, child *Widget) {
	cwindow, cwindowAllocMap := (*C.GtkWindow)(unsafe.Pointer(window)), cgoAllocsUnknown
	cchild, cchildAllocMap := (*C.GtkWidget)(unsafe.Pointer(child)), cgoAllocsUnknown
	C.gtk_window_set_child(cwindow, cchild)
	runtime.KeepAlive(cchildAllocMap)
	runtime.KeepAlive(cwindowAllocMap)
}

// WidgetShow function as declared in gtk4go/gtk4_simple.h:64
func WidgetShow(widget *Widget) {
	cwidget, cwidgetAllocMap := (*C.GtkWidget)(unsafe.Pointer(widget)), cgoAllocsUnknown
	C.gtk_widget_show(cwidget)
	runtime.KeepAlive(cwidgetAllocMap)
}

// Init function as declared in gtk4go/gtk4_simple.h:71
func Init() {
	C.gtk_init()
}

// InitCheck function as declared in gtk4go/gtk4_simple.h:72
func InitCheck() int32 {
	__ret := C.gtk_init_check()
	__v := (int32)(__ret)
	return __v
}
