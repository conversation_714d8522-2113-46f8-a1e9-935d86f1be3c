{"app-id": "org.gtk.Demo4", "runtime": "org.gnome.Platform", "runtime-version": "master", "sdk": "org.gnome.Sdk", "command": "gtk4-demo", "tags": ["devel", "development", "nightly"], "desktop-file-name-prefix": "(Development) ", "finish-args": ["--device=dri", "--share=ipc", "--socket=fallback-x11", "--socket=wayland", "--talk-name=org.gtk.vfs", "--talk-name=org.gtk.vfs.*"], "cleanup": ["/include", "/lib/pkgconfig", "/share/pkgconfig", "/share/aclocal", "/man", "/share/man", "/share/gtk-doc", "*.la", ".a", "/lib/girepository-1.0", "/share/gir-1.0", "/share/doc"], "modules": [{"name": "wayland", "buildsystem": "meson", "builddir": true, "config-opts": ["-Ddocumentation=false"], "sources": [{"type": "git", "url": "https://gitlab.freedesktop.org/wayland/wayland.git", "branch": "main"}]}, {"name": "graphene", "buildsystem": "meson", "builddir": true, "config-opts": ["--libdir=/app/lib", "-Dtests=false"], "sources": [{"type": "git", "url": "https://github.com/ebassi/graphene.git"}]}, {"name": "libsass", "buildsystem": "meson", "builddir": true, "config-opts": ["--libdir=/app/lib"], "sources": [{"type": "git", "url": "https://github.com/lazka/libsass.git", "branch": "meson"}]}, {"name": "sassc", "buildsystem": "meson", "builddir": true, "config-opts": ["--libdir=/app/lib"], "sources": [{"type": "git", "url": "https://github.com/lazka/sassc.git", "branch": "meson"}]}, {"name": "boost", "buildsystem": "simple", "build-commands": ["./bootstrap.sh --prefix=/app --with-libraries=date_time,filesystem,iostreams,locale,regex,system,thread,python,program_options,test,serialization", "./b2 --build-type=minimal link=shared -j $FLATPAK_BUILDER_N_JOBS", "./b2 --build-type=minimal link=shared install"], "sources": [{"type": "archive", "url": "https://archives.boost.io/release/1.79.0/source/boost_1_79_0.tar.bz2", "sha256": "475d589d51a7f8b3ba2ba4eda022b170e562ca3b760ee922c146b6c65856ef39"}]}, {"name": "lua-5.1", "buildsystem": "simple", "build-commands": ["make -j $FLATPAK_BUILDER_N_JOBS CFLAGS=\"$CFLAGS -fPIC -DLUA_USE_LINUX\" linux", "make INSTALL_TOP=$FLATPAK_DEST TO_LIB='liblua.a liblua.so.5.1.5' install", "ln -sf liblua.so.5.1.5 $FLATPAK_DEST/lib/liblua.so", "ln -sf liblua.so.5.1.5 $FLATPAK_DEST/lib/liblua.so.5.1", "install -Dm0644 etc/lua.pc $FLATPAK_DEST/lib/pkgconfig/lua.pc", "ln -sf lua.pc $FLATPAK_DEST/lib/pkgconfig/lua51.pc", "ln -sf lua.pc $FLATPAK_DEST/lib/pkgconfig/lua5.1.pc", "ln -sf lua.pc $FLATPAK_DEST/lib/pkgconfig/lua-5.1.pc"], "sources": [{"type": "archive", "url": "https://www.lua.org/ftp/lua-5.1.5.tar.gz", "sha256": "2640fc56a795f29d28ef15e13c34a47e223960b0240e8cb0a82d9b0738695333"}, {"type": "patch", "path": "lua-5.1.5-so.patch"}, {"type": "shell", "commands": ["sed -i \"s|/usr/local|$FLATPAK_DEST|\" etc/lua.pc src/luaconf.h"]}], "cleanup": ["*.a", "/bin", "/include", "/lib/pkgconfig", "/man"]}, {"name": "highlight", "buildsystem": "simple", "builddir": true, "build-commands": ["sed -i -e 's#^PREFIX = /usr#PREFIX = /app#' makefile", "make", "make install"], "sources": [{"type": "archive", "url": "http://www.andre-simon.de/zip/highlight-4.0.tar.bz2", "sha256": "f40dcba26e011a2c67df874f4d9b0238c2c6b065163ce8de3d8371b9dfce864d"}]}, {"name": "pango", "buildsystem": "meson", "builddir": true, "config-opts": ["--libdir=/app/lib"], "sources": [{"type": "git", "url": "https://gitlab.gnome.org/GNOME/pango.git", "branch": "main"}]}, {"name": "gtk", "buildsystem": "meson", "builddir": true, "config-opts": ["--libdir=/app/lib", "-Dbuildtype=debugoptimized", "-Dprofile=devel"], "sources": [{"type": "git", "url": "https://gitlab.gnome.org/GNOME/gtk.git", "branch": "main"}]}]}