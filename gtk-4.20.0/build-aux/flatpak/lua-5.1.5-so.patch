diff -ur lua-5.1.4/src/Makefile lua-5.1.4-new/src/Makefile
--- lua-5.1.4/src/Makefile	2008-01-19 20:37:58.000000000 +0100
+++ lua-5.1.4-new/src/Makefile	2012-02-23 18:26:43.000000000 +0100
@@ -23,6 +23,7 @@
 PLATS= aix ansi bsd freebsd generic linux macosx mingw posix solaris
 
 LUA_A=	liblua.a
+LUA_SO=	liblua.so
 CORE_O=	lapi.o lcode.o ldebug.o ldo.o ldump.o lfunc.o lgc.o llex.o lmem.o \
 	lobject.o lopcodes.o lparser.o lstate.o lstring.o ltable.o ltm.o  \
 	lundump.o lvm.o lzio.o
@@ -36,7 +37,7 @@
 LUAC_O=	luac.o print.o
 
 ALL_O= $(CORE_O) $(LIB_O) $(LUA_O) $(LUAC_O)
-ALL_T= $(LUA_A) $(LUA_T) $(LUAC_T)
+ALL_T= $(LUA_A) $(LUA_SO) $(LUA_T) $(LUAC_T)
 ALL_A= $(LUA_A)
 
 default: $(PLAT)
@@ -51,6 +52,11 @@
 	$(AR) $@ $?
 	$(RANLIB) $@
 
+$(LUA_SO): $(CORE_O) $(LIB_O)
+	$(CC) -shared -ldl -Wl,-soname,$(LUA_SO).$(V) -o $@.$(R) $? -lm $(MYLDFLAGS)
+	ln -sf $(LUA_SO).$(R) $(LUA_SO).$(V)
+	ln -sf $(LUA_SO).$(R) $(LUA_SO)
+
 $(LUA_T): $(LUA_O) $(LUA_A)
 	$(CC) -o $@ $(MYLDFLAGS) $(LUA_O) $(LUA_A) $(LIBS)
 
--- lua-5.1.4/Makefile	2008-08-12 02:40:48.000000000 +0200
+++ lua-5.1.4-new/Makefile	2012-02-23 19:06:32.000000000 +0100
@@ -53,7 +53,7 @@
 all:	$(PLAT)
 
 $(PLATS) clean:
-	cd src && $(MAKE) $@
+	cd src && $(MAKE) $@ V=$(V) R=$(R)
 
 test:	dummy
 	src/lua test/hello.lua
