/*
 * SPDX-FileCopyrightText: 2021 GNOME Foundation
 *
 * SPDX-License-Identifier: LGPL-2.1-or-later
 */

/**
 * RedHat Fonts taken from https://github.com/RedHatOfficial/RedHatFont
 * License: SIL Open Font License 1.1 http://scripts.sil.org/OFL
 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Serif:ital,wght@0,400;0,700;1,400;1,700&family=Red+Hat+Display:ital,wght@0,400;0,500;0,700;0,900;1,400;1,500;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Serif:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Source+Code+Pro:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,600&display=swap');

@font-face {
  font-family: "RedHatDisplayWeb";
  src: local('RedHatDisplayWeb'),
       url("RedHatDisplay-Regular.woff2") format("woff2"),
       url("RedHatDisplay-Regular.woff") format("woff");
  font-style: normal;
  font-weight: 400;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatDisplayWeb";
  src: local('RedHatDisplayWeb'),
       url("RedHatDisplay-RegularItalic.woff2") format("woff2"),
       url("RedHatDisplay-RegularItalic.woff") format("woff");
  font-style: italic;
  font-weight: 400;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatDisplayWeb";
  src: local('RedHatDisplayWeb'),
       url("RedHatDisplay-Medium.woff2") format("woff2"),
       url("RedHatDisplay-Medium.woff") format("woff");
  font-style: normal;
  font-weight: 500;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatDisplayWeb";
  src: local('RedHatDisplayWeb'),
       url("RedHatDisplay-MediumItalic.woff2") format("woff2"),
       url("RedHatDisplay-MediumItalic.woff") format("woff");
  font-style: italic;
  font-weight: 500;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatDisplayWeb";
  src: local('RedHatDisplayWeb'),
       url("RedHatDisplay-Bold.woff2") format("woff2"),
       url("RedHatDisplay-Bold.woff") format("woff");
  font-style: normal;
  font-weight: 700;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatDisplayWeb";
  src: local('RedHatDisplayWeb'),
       url("RedHatDisplay-BoldItalic.woff2") format("woff2"),
       url("RedHatDisplay-BoldItalic.woff") format("woff");
  font-style: italic;
  font-weight: 700;
  font-display: fallback;
}


@font-face {
  font-family: "RedHatDisplayWeb";
  src: local('RedHatDisplayWeb'),
       url("RedHatDisplay-Black.woff2") format("woff2"),
       url("RedHatDisplay-Black.woff") format("woff");
  font-style: normal;
  font-weight: 900;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatDisplayWeb";
  src: local('RedHatDisplayWeb'),
       url("RedHatDisplay-BlackItalic.woff2") format("woff2"),
       url("RedHatDisplay-BlackItalic.woff") format("woff");
  font-style: italic;
  font-weight: 900;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatTextWeb";
  src: local('RedHatTextWeb'),
       url("RedHatText-Regular.woff2") format("woff2"),
       url("RedHatText-Regular.woff") format("woff");
  font-style: normal;
  font-weight: 400;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatTextWeb";
  src: local('RedHatTextWeb'),
       url("RedHatText-RegularItalic.woff2") format("woff2"),
       url("RedHatText-RegularItalic.woff") format("woff");
  font-style: italic;
  font-weight: 400;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatTextWeb";
  src: local('RedHatTextWeb'),
       url("RedHatText-Medium.woff2") format("woff2"),
       url("RedHatText-Medium.woff") format("woff");
  font-style: normal;
  font-weight: 700;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatTextWeb";
  src: local('RedHatTextWeb'),
       url("RedHatText-MediumItalic.woff2") format("woff2"),
       url("RedHatText-MediumItalic.woff") format("woff");
  font-style: italic;
  font-weight: 700;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatTextWeb";
  src: local('RedHatTextWeb'),
       url("RedHatText-Bold.woff2") format("woff2"),
       url("RedHatText-Bold.woff") format("woff");
  font-style: normal;
  font-weight: 900;
  font-display: fallback;
}

@font-face {
  font-family: "RedHatTextWeb";
  src: local('RedHatTextWeb'),
       url("RedHatText-BoldItalic.woff2") format("woff2"),
       url("RedHatText-BoldItalic.woff") format("woff");
  font-style: italic;
  font-weight: 900;
  font-display: fallback;
}
