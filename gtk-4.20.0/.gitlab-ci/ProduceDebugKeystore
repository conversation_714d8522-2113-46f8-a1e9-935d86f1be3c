#!/usr/bin/env -S java --source 11 -cp ${STUDIO_DIR}/lib/lib.jar:${STUDIO_DIR}/lib/util-8.jar:${STUDIO_DIR}/lib/bouncy-castle.jar:${STUDIO_DIR}/plugins/android/lib/android-base-common.jar:${STUDIO_DIR}/plugins/android/lib/sdk-common.jar:${BUILDER_JAR}
// BUILDER_JAR is https://mvnrepository.com/artifact/com.android.tools.build/builder

import com.android.builder.signing.DefaultSigningConfig;
import com.android.ide.common.signing.KeystoreHelper;
import com.android.ide.common.signing.KeytoolException;
import com.android.utils.ILogger;
import com.android.utils.StdLogger;
import java.io.File;
import java.security.KeyStore;

public final class ProduceDebugKeystore {
	public static void main(String[] args) throws KeytoolException {
		if (args.length < 1)
			throw new RuntimeException("Expected path to write keystore into");
		ILogger logger = new StdLogger(StdLogger.Level.WARNING);
		boolean success = KeystoreHelper.createDebugStore(
			KeyStore.getDefaultType(),
			new File(args[0]),
			DefaultSigningConfig.DEFAULT_PASSWORD,
			DefaultSigningConfig.DEFAULT_PASSWORD,
			DefaultSigningConfig.DEFAULT_ALIAS,
			logger
		);
		if (!success)
			System.exit(1);
	}
}
