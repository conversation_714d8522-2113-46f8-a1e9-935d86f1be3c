Title: Paths
Slug: paths

GSK provides a path API that can be used to render more complex
shapes than lines or rounded rectangles. It is comparable to cairos
[path API](https://www.cairographics.org/manual/cairo-Paths.html),
with some notable differences.

In general, a path consists of one or more connected **_contours_**,
each of which may have multiple **_operations_**, and may or may not be closed.
Operations can be straight lines or curves of various kinds. At the points
where the operations connect, the path can have sharp turns.

<figure>
  <picture>
    <source srcset="path-dark.png" media="(prefers-color-scheme: dark)">
    <img alt="A path with multiple contours" src="path-light.png">
  </picture>
  <figcaption>A path with one closed, and one open contour</figcaption>
</figure>

The central object of the GSK path API is the immutable [<EMAIL>]
struct, which contains path data in compact form suitable for rendering.

## Creating Paths

Since `GskPath` is immutable, the auxiliary [<EMAIL>] struct
can be used to construct a path piece by piece. The pieces are specified with
points, some of which will be on the path, while others are just **_control points_**
that are used to influence the shape of the resulting path.

<figure>
  <picture>
    <source srcset="cubic-dark.png" media="(prefers-color-scheme: dark)">
    <img alt="An cubic Bézier" src="cubic-light.png">
  </picture>
  <figcaption>A cubic Bézier operation, with 2 control points</figcaption>
</figure>

The `GskPathBuilder` API has three distinct groups of functions:

- Functions for building contours from individual operations, like [<EMAIL>.move_to],
  [<EMAIL>.line_to], [<EMAIL>.cubic_to], [<EMAIL>]. `GskPathBuilder` maintains a **_current point_**, so these methods all
  take one less points than necessary for the operation (e.g. `gsk_path_builder_line_to`
  only takes a single point and draws a line from the current point to the new point).

- Functions for adding complete contours, such as [<EMAIL>.add_rect],
  [<EMAIL>.add_rounded_rect], [<EMAIL>.add_circle].

- Adding parts of a preexisting path. Functions in this group include
  [<EMAIL>.add_path] and [<EMAIL>.add_segment].

When you are done with building a path, you can convert the accumulated path
data into a `GskPath` struct with [<EMAIL>.free_to_path].

A sometimes convenient alternative is to create a path from a serialized form,
with [<EMAIL>]. This function interprets strings in SVG path syntax,
such as:

    M 100 100 C *********** *********** Z

## Rendering with Paths

There are two main ways to render with paths. The first is to **_fill_** the
interior of a path with a color or more complex content, such as a gradient.
GSK supports different ways of determining what part of the plane are interior
to the path, which can be selected with a [<EMAIL>] value.

<figure>
  <picture>
    <img alt="A filled path" src="fill-winding.png">
  </picture>
  <figcaption>A path filled with GSK_FILL_RULE_WINDING</figcaption>
</figure>

<figure>
  <picture>
    <img alt="A filled path" src="fill-even-odd.png">
  </picture>
  <figcaption>The same path, filled with GSK_FILL_RULE_EVEN_ODD</figcaption>
</figure>

To fill a path, use [gtk_snapshot_append_fill()](../gtk4/method.Snapshot.append_fill.html)
or the more general [gtk_snapshot_push_fill()](../gtk4/method.Snapshot.push_fill.html).

Alternatively, a path can be **_stroked_**, which means to emulate drawing
with an idealized pen along the path. The result of stroking a path is another
path (the **_stroke path_**), which is then filled.

The stroke operation can be influenced with the [<EMAIL>] struct
that collects various stroke parameters, such as the line width, the style
of line joins and line caps, and a dash pattern.

<figure>
  <picture>
    <img alt="A stroked path" src="stroke-miter.png">
  </picture>
  <figcaption>The same path, stroked with GSK_LINE_JOIN_MITER</figcaption>
</figure>

<figure>
  <picture>
    <img alt="A stroked path" src="stroke-round.png">
  </picture>
  <figcaption>The same path, stroked with GSK_LINE_JOIN_ROUND</figcaption>
</figure>

To stroke a path, use
[gtk_snapshot_append_stroke()](../gtk4/method.Snapshot.append_stroke.html)
or [gtk_snapshot_push_stroke()](../gtk4/method.Snapshot.push_stroke.html).

## Hit testing

When paths are rendered as part of an interactive interface, it is sometimes
necessary to determine whether the mouse points is over the path. GSK provides
[<EMAIL>.in_fill] for this purpose.

## Path length

An important property of paths is their **_length_**. Computing it efficiently
requires caching, therefore GSK provides a separate [<EMAIL>] object
to deal with path lengths. After constructing a `GskPathMeasure` object for a path,
it can be used to determine the length of the path with [<EMAIL>.get_length]
and locate points at a given distance into the path with [<EMAIL>.get_point].

## Other Path APIs

Paths have uses beyond rendering, for example as trajectories in animations.
In such uses, it is often important to access properties of paths, such as
their tangents at certain points. GSK provides an abstract representation
for points on a path in the form of the [<EMAIL>] struct.
You can query properties of a path at certain point once you have a
`GskPathPoint` representing that point.

`GskPathPoint` structs can be compared for equality with [<EMAIL>]
and ordered wrt. to which one comes first, using [<EMAIL>].

To obtain a `GskPathPoint`, use [<EMAIL>.get_closest_point],
[<EMAIL>.get_start_point], [<EMAIL>.get_end_point] or
[<EMAIL>.get_point].

To query properties of the path at a point, use [<EMAIL>.get_position],
[<EMAIL>.get_tangent], [<EMAIL>.get_rotation],
[<EMAIL>.get_curvature] and [<EMAIL>.get_distance].

Some of the properties can have different values for the path going into
the point and the path leaving the point, typically at points where the
path takes sharp turns. Examples for this are tangents (which can have up
to 4 different values) and curvatures (which can have two different values).

<figure>
  <picture>
    <source srcset="directions-dark.png" media="(prefers-color-scheme: dark)">
    <img alt="Path Tangents" src="directions-light.png">
  </picture>
  <figcaption>Path Tangents</figcaption>
</figure>

## Going beyond GskPath

Lots of powerful functionality can be implemented for paths:

- Finding intersections
- Offsetting curves
- Turning stroke outlines into paths
- Molding curves (making them pass through a given point)

GSK does not provide API for all of these, but it does offer a way to get at
the underlying Bézier curves, so you can implement such functionality yourself.
You can use [<EMAIL>] to iterate over the operations of the
path, and get the points needed to reconstruct or modify the path piece by piece.

See e.g. the [Primer on Bézier curves](https://pomax.github.io/bezierinfo/)
for inspiration of useful things to explore.
