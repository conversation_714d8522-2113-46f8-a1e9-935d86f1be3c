.. _gtk4-path-tool(1):

=================
gtk4-path-tool
=================

-----------------------
GskPath Utility
-----------------------

:Version: GTK
:Manual section: 1
:Manual group: GTK commands

SYNOPSIS
--------
|   **gtk4-path-tool** <COMMAND> [OPTIONS...] <PATH>
|
|   **gtk4-path-tool** decompose [OPTIONS...] <PATH>
|   **gtk4-path-tool** show [OPTIONS...] <PATH>...
|   **gtk4-path-tool** render [OPTIONS...] <PATH>...
|   **gtk4-path-tool** reverse [OPTIONS...] <PATH>
|   **gtk4-path-tool** info [OPTIONS...] <PATH>

DESCRIPTION
-----------

``gtk4-path-tool`` can perform various tasks on paths. Paths are specified
in SVG syntax, as strings like "M 100 100 C *********** *********** Z".

To read a path from a file, use a filename that starts with a '.' or a '/'.
To read a path from stdin, use '-'.

COMMANDS
--------

Decomposing
^^^^^^^^^^^

The ``decompose`` command approximates the path by one with simpler elements.
When used without options, the curves of the path are approximated by line
segments.

``--allow-quad``

  Allow quadratic Bézier curves to be used in the generated path.

``--allow-cubic``

  Allow cubic Bézier curves to be used in the generated path.

``--allow-conic``

  Allow conic Bézier curves to be used in the generated path.

Showing
^^^^^^^

The ``show`` command displays the given path in a window. The interior
of the path is filled. The window allows some interactive control with
key bindings: '+'/'-' change the zoom level, 'p' toggles display of points,
'c' toggles display of controls, 'i' toggles display of intersections and
'f' changes the fill rule.

``--fill``

  Fill the path (this is the default).

``--stroke``

  Stroke the path instead of filling it.

``--points``

  Show points on the path.

``--controls``

  Show control points.

``--intersections``

  If two paths are given, show their intersections. If one path is given,
  show its self-intersections.

``--fill-rule=VALUE``

  The fill rule that is used to determine what areas are inside the path.
  The possible values are ``winding`` or ``even-odd``. The default is ``winding``.

``--fg-color=COLOR``

  The color that is used to fill the interior of the path or stroke the path.
  If not specified, black is used.

``--bg-color=COLOR``

  The color that is used to render the background behind the path.
  If not specified, white is used.

``--point-color=COLOR``

  The color that is used to render the points.
  If not specified, red is used.

``--intersection-color=COLOR``

  The color that is used to render intersections.
  If not specified, green is used.

``--line-width=VALUE``

  The line width to use for the stroke. ``VALUE`` must be a positive number.
  The default line width is 1.

``--line-cap=VALUE``

  The cap style to use at line ends. The possible values are ``butt``, ``round``
  or ``square``. See the SVG specification for details on these styles.
  The default cap style is ``butt``.

``--line-join=VALUE``

  The join style to use at line joins. The possible values are ``miter``,
  ``miter-clip``, ``round``, ``bevel`` or ``arcs``. See the SVG specification
  for details on these styles.
  The default join style is ``miter``.

``--miter-limit=VALUE``

  The limit at which to clip miters at line joins. The default value is 4.

``--dashes=VALUE``

  The dash pattern to use for this stroke. A dash pattern is specified by
  a comma-separated list of alternating non-negative numbers. Each number
  provides the length of alternate "on" and "off" portions of the stroke.
  If the dash pattern is empty, dashing is disabled, which is the default.
  See the SVG specification for details on dashing.

``--dash-offset=VALUE``

  The offset into the dash pattern where dashing should begin.
  The default value is 0.

``--zoom=NUMBER``

  Set the zoom level to the given number (between 1 and 20).

Rendering
^^^^^^^^^

The ``render`` command renders the given path as a PNG image.

``--fill``

  Fill the path (this is the default).

``--stroke``

  Stroke the path instead of filling it.

``--points``

  Show points on the path.

``--controls``

  Show control points.

``--intersections``

  If two paths are given, show their intersections. If one path is given,
  show its self-intersections.

``--fill-rule=VALUE``

  The fill rule that is used to determine what areas are inside the path.
  The possible values are ``winding`` or ``even-odd``. The default is ``winding``.

``--fg-color=COLOR``

  The color that is used to fill the interior of the path or stroke the path.
  If not specified, black is used.

``--bg-color=COLOR``

  The color that is used to render the background behind the path.
  If not specified, white is used.

``--point-color=COLOR``

  The color that is used to render the points.
  If not specified, red is used.

``--intersection-color=COLOR``

  The color that is used to render intersections.
  If not specified, green is used.

``--output-file=FILE``

  The file to save the PNG image to.
  If not specified, "path.png" is used.

``--line-width=VALUE``

  The line width to use for the stroke. ``VALUE`` must be a positive number.
  The default line width is 1.

``--line-cap=VALUE``

  The cap style to use at line ends. The possible values are ``butt``, ``round``
  or ``square``. See the SVG specification for details on these styles.
  The default cap style is ``butt``.

``--line-join=VALUE``

  The join style to use at line joins. The possible values are ``miter``,
  ``miter-clip``, ``round``, ``bevel`` or ``arcs``. See the SVG specification
  for details on these styles.
  The default join style is ``miter``.

``--miter-limit=VALUE``

  The limit at which to clip miters at line joins. The default value is 4.

``--dashes=VALUE``

  The dash pattern to use for this stroke. A dash pattern is specified by
  a comma-separated list of alternating non-negative numbers. Each number
  provides the length of alternate "on" and "off" portions of the stroke.
  If the dash pattern is empty, dashing is disabled, which is the default.
  See the SVG specification for details on dashing.

``--dash-offset=VALUE``

  The offset into the dash pattern where dashing should begin.
  The default value is 0.

``--zoom=NUMBER``

  Set the zoom level to the given number (between 1 and 20).

Reversing
^^^^^^^^^

The ``reverse`` command changes the direction of the path. The resulting
paths starts where the original path ends.

Info
^^^^

The ``info`` command shows various information about the given path,
such as its bounding box.

REFERENCES
----------

- SVG Path Specification, https://www.w3.org/TR/SVG2/paths.html
