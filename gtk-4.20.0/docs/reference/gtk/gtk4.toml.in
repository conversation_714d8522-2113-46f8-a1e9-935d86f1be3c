[library]
version = "@version@"
browse_url = "https://gitlab.gnome.org/GNOME/gtk/"
repository_url = "https://gitlab.gnome.org/GNOME/gtk.git"
website_url = "https://www.gtk.org"
docs_url = "https://docs.gtk.org/gtk4/"
authors = "GTK Development Team"
logo_url = "gtk-logo.svg"
license = "LGPL-2.1-or-later"
description = "The GTK toolkit"
devhelp = true
search_index = true

dependencies = ["Gdk-4.0", "Gsk-4.0"]

  [dependencies."Gdk-4.0"]
  name = "Gdk"
  description = "The GTK windowing system abstraction"
  docs_url = "https://docs.gtk.org/gdk4/"

  [dependencies."Gsk-4.0"]
  name = "Gsk"
  description = "The GTK rendering abstraction"
  docs_url = "https://docs.gtk.org/gsk4/"

related = ["Pango-1.0", "Graphene-1.0", "GObject-2.0", "Gio-2.0"]

  [related."GObject-2.0"]
  name = "GObject"
  description = "The base type system library"
  docs_url = "https://docs.gtk.org/gobject/"

  [related."Gio-2.0"]
  name = "GIO"
  description = "GObject Interfaces and Objects, Networking, IPC, and I/O"
  docs_url = "https://docs.gtk.org/gio/"

  [related."Graphene-1.0"]
  name = "Graphene"
  description = "A thin layer of mathematical types for 3D libraries"
  docs_url = "https://ebassi.github.io/graphene/docs"

  [related."Pango-1.0"]
  name = "Pango"
  description = "Text shaping and rendering"
  docs_url = "https://docs.gtk.org/Pango/"

[theme]
name = "basic"
show_index_summary = true
show_class_hierarchy = true

[source-location]
base_url = "https://gitlab.gnome.org/GNOME/gtk/-/blob/main/"

[extra]
# The same order will be used when generating the index
content_files = [
  "overview.md",
  "getting_started.md",
  "building.md",
  "compiling.md",
  "running.md",
  "question_index.md",
  "resources.md",
  "initialization.md",
  "actions.md",
  "input-handling.md",
  "drag-and-drop.md",
  "drawing-model.md",
  "coordinates.md",
  "css-overview.md",
  "css-properties.md",
  "section-accessibility.md",
  "section-dialogs.md",
  "section-list-widget.md",
  "section-text-widget.md",
  "section-tree-widget.md",
  "migrating-2to4.md",
  "migrating-3to4.md",
  "migrating-4to5.md",
  "broadway.md",
  "osx.md",
  "wayland.md",
  "windows.md",
  "x11.md",
  "tools.md",
  "visual_index.md",
]
content_images = [
  "../images/favicon.svg",
  "../images/favicon-192x192.png",
  "images/aboutdialog.png",
  "images/aboutdialog-dark.png",
  "images/action-bar.png",
  "images/action-bar-dark.png",
  "images/appchooserbutton.png",
  "images/appchooserbutton-dark.png",
  "images/appchooserdialog.png",
  "images/appchooserdialog-dark.png",
  "images/arrows.png",
  "images/assistant.png",
  "images/assistant-dark.png",
  "images/background.png",
  "images/bloatpad-gnome.png",
  "images/bloatpad-osx.png",
  "images/bloatpad-xfce.png",
  "images/border1.png",
  "images/border2.png",
  "images/border3.png",
  "images/box.png",
  "images/box-dark.png",
  "images/box-expand.png",
  "images/box-packing.png",
  "images/builder-shortcuts.png",
  "images/builder-shortcuts-dark.png",
  "images/button.png",
  "images/button-dark.png",
  "images/calendar.png",
  "images/calendar-dark.png",
  "images/capture-bubble.png",
  "images/centerbox.png",
  "images/centerbox-dark.png",
  "images/check-button.png",
  "images/check-button-dark.png",
  "images/checks.png",
  "images/clocks-shortcuts.png",
  "images/clocks-shortcuts-dark.png",
  "images/color-button.png",
  "images/color-button-dark.png",
  "images/colorchooser.png",
  "images/colorchooser-dark.png",
  "images/combo-box-entry.png",
  "images/combo-box-entry-dark.png",
  "images/combo-box.png",
  "images/combo-box-dark.png",
  "images/combo-box-text.png",
  "images/combo-box-text-dark.png",
  "images/dialog.png",
  "images/dialog-dark.png",
  "images/down-center.png",
  "images/down-end.png",
  "images/down-start.png",
  "images/drop-down.png",
  "images/drop-down-dark.png",
  "images/drawing.png",
  "images/drawingarea.png",
  "images/drawingarea-dark.png",
  "images/ease-in-out.png",
  "images/ease-in.png",
  "images/ease-out.png",
  "images/ease.png",
  "images/editable-label.png",
  "images/editable-label-dark.png",
  "images/emojichooser.png",
  "images/emojichooser-dark.png",
  "images/entry.png",
  "images/entry-dark.png",
  "images/exampleapp.png",
  "images/expanders.png",
  "images/expander.png",
  "images/expander-dark.png",
  "images/extensions.png",
  "images/figure-hierarchical-drawing.png",
  "images/figure-windowed-label.png",
  "images/file-button.png",
  "images/filechooser.png",
  "images/flow-box.png",
  "images/flow-box-dark.png",
  "images/focus.png",
  "images/font-button.png",
  "images/font-button-dark.png",
  "images/fontchooser.png",
  "images/fontchooser-dark.png",
  "images/frame-gap.png",
  "images/frame.png",
  "images/frame-dark.png",
  "images/frames.png",
  "images/gedit-shortcuts.png",
  "images/gedit-shortcuts-dark.png",
  "images/getting-started-app10.png",
  "images/getting-started-app1.png",
  "images/getting-started-app2.png",
  "images/getting-started-app3.png",
  "images/getting-started-app4.png",
  "images/getting-started-app6.png",
  "images/getting-started-app7.png",
  "images/getting-started-app8.png",
  "images/getting-started-app9.png",
  "images/glarea.png",
  "images/glarea-dark.png",
  "images/gradient1.png",
  "images/gradient2.png",
  "images/gradient3.png",
  "images/gradient4.png",
  "images/grid.png",
  "images/grid-dark.png",
  "images/grid-packing.png",
  "images/gtk-logo.png",
  "images/gtk-logo.svg",
  "images/handles.png",
  "images/headerbar.png",
  "images/headerbar-dark.png",
  "images/hello-world.png",
  "images/icon-view.png",
  "images/icon-view-dark.png",
  "images/image.png",
  "images/image-dark.png",
  "images/info-bar.png",
  "images/info-bar-dark.png",
  "images/inspector.png",
  "images/label.png",
  "images/label-dark.png",
  "images/layout-btlr.png",
  "images/layout-btrl.png",
  "images/layout-lrbt.png",
  "images/layout-lrtb.png",
  "images/layout-rlbt.png",
  "images/layout-rltb.png",
  "images/layout-tblr.png",
  "images/layout-tbrl.png",
  "images/left-center.png",
  "images/left-end.png",
  "images/left-start.png",
  "images/levelbar.png",
  "images/levelbar-dark.png",
  "images/linear.png",
  "images/link-button.png",
  "images/link-button-dark.png",
  "images/list-and-tree.png",
  "images/list-and-tree-dark.png",
  "images/list-box.png",
  "images/list-box-dark.png",
  "images/lockbutton.png",
  "images/lockbutton-dark.png",
  "images/lockbutton-sorry.png",
  "images/lockbutton-sorry-dark.png",
  "images/lockbutton-unlocked.png",
  "images/lockbutton-unlocked-dark.png",
  "images/macos-window-controls.png",
  "images/media-controls.png",
  "images/media-controls-dark.png",
  "images/menu.png",
  "images/menu-dark.png",
  "images/menubar.png",
  "images/menubar-dark.png",
  "images/menu-button.png",
  "images/menu-button-dark.png",
  "images/messagedialog.png",
  "images/messagedialog-dark.png",
  "images/multiline-text.png",
  "images/multiline-text-dark.png",
  "images/notebook.png",
  "images/notebook-dark.png",
  "images/options.png",
  "images/overlay.png",
  "images/overlay-dark.png",
  "images/pagesetupdialog.png",
  "images/pagesetupdialog-dark.png",
  "images/panes.png",
  "images/panes-dark.png",
  "images/password-entry.png",
  "images/password-entry-dark.png",
  "images/picture.png",
  "images/picture-dark.png",
  "images/popover.png",
  "images/popover-dark.png",
  "images/printdialog.png",
  "images/printdialog-dark.png",
  "images/progressbar.png",
  "images/progressbar-dark.png",
  "images/radio-button.png",
  "images/radio-button-dark.png",
  "images/right-center.png",
  "images/right-end.png",
  "images/right-start.png",
  "images/scales.png",
  "images/scales-dark.png",
  "images/scrollbar.png",
  "images/scrollbar-dark.png",
  "images/scrolledwindow.png",
  "images/scrolledwindow-dark.png",
  "images/search-bar.png",
  "images/search-bar-dark.png",
  "images/search-entry.png",
  "images/search-entry-dark.png",
  "images/separator.png",
  "images/separator-dark.png",
  "images/shortcuts-window.png",
  "images/shortcuts-window-dark.png",
  "images/sidebar.png",
  "images/sidebar-dark.png",
  "images/slices.png",
  "images/sliders.png",
  "images/spinbutton.png",
  "images/spinbutton-dark.png",
  "images/spinner.png",
  "images/spinner-dark.png",
  "images/stack.png",
  "images/stack-dark.png",
  "images/stackswitcher.png",
  "images/stackswitcher-dark.png",
  "images/statusbar.png",
  "images/statusbar-dark.png",
  "images/switch.png",
  "images/switch-dark.png",
  "images/toggle-button.png",
  "images/toggle-button-dark.png",
  "images/toolbar.png",
  "images/tree-view-coordinates.png",
  "images/up-center.png",
  "images/up-end.png",
  "images/up-start.png",
  "images/video.png",
  "images/video-dark.png",
  "images/volumebutton.png",
  "images/volumebutton-dark.png",
  "images/widget-hvalign.png",
  "images/windowcontrols.png",
  "images/windowcontrols-dark.png",
  "images/window-default.png",
  "images/window.png",
  "images/window-dark.png",
  "images/rich-list.png",
  "images/data-table.png",
  "images/navigation-sidebar.png",
  "images/box-model-light.png",
  "images/box-model-dark.png",
]
content_base_url = "https://gitlab.gnome.org/GNOME/gtk/-/blob/main/docs/reference/gtk/"
urlmap_file = "urlmap.js"

[[object]]
name = "StyleProvider"
  [[object.signal]]
  name = "gtk-private-changed"
  hidden = true

[check]
ignore_deprecated = true
