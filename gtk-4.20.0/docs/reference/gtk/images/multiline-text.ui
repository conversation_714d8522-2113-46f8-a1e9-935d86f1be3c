<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="decorated">0</property>
    <property name="resizable">0</property>
    <property name="default-width">280</property>
    <property name="default-height">120</property>
    <style>
       <class name="nobackground"/>
    </style>
    <child>
      <object class="GtkBox">
        <style>
          <class name="shadow"/>
          <class name="background"/>
          <class name="frame"/>
        </style>
        <child>
          <object class="GtkFrame">
            <property name="margin-start">10</property>
            <property name="margin-end">10</property>
            <property name="margin-top">10</property>
            <property name="margin-bottom">10</property>
            <child>
              <object class="GtkTextView">
                <property name="hexpand">1</property>
                <property name="vexpand">1</property>
                <property name="buffer">
                  <object class="GtkTextBuffer">
                    <property name="text">Multiline
Text

                    </property>
                  </object>
                </property>
                <property name="cursor-visible">0</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
