# commented-out ui files need more work

ui_files = [
  'aboutdialog.ui',
  'action-bar.ui',
  'appchooserbutton.ui',
  'appchooserdialog.ui',
  'assistant.ui',
  'box.ui',
  'builder-shortcuts.ui',
  'button.ui',
  'calendar.ui',
  'centerbox.ui',
  'check-button.ui',
  'clocks-shortcuts.ui',
  'colorchooser.ui',
  'color-button.ui',
  'combo-box.ui',
  'combo-box-entry.ui',
  'combo-box-text.ui',
  'dialog.ui',
  'drawingarea.ui',
  'drop-down.ui',
  'editable-label.ui',
  'emojichooser.ui',
  'entry.ui',
  'expander.ui',
  'flow-box.ui',
  'fontchooser.ui',
  'font-button.ui',
  'frame.ui',
  'gedit-shortcuts.ui',
  'glarea.ui',
  'grid.ui',
  'headerbar.ui',
  'icon-view.ui',
  'image.ui',
  'info-bar.ui',
  'label.ui',
  'levelbar.ui',
  'link-button.ui',
  'list-box.ui',
  'list-and-tree.ui',
  'media-controls.ui',
  'menu.ui',
  'menubar.ui',
  'menu-button.ui',
  'messagedialog.ui',
  'multiline-text.ui',
  'notebook.ui',
  'overlay.ui',
  'pagesetupdialog.ui',
  'panes.ui',
  'password-entry.ui',
  'picture.ui',
  #'popover.ui',
  'printdialog.ui',
  'progressbar.ui',
  'radio-button.ui',
  'scales.ui',
  'scrollbar.ui',
  'scrolledwindow.ui',
  'search-bar.ui',
  'search-entry.ui',
  'separator.ui',
  'shortcuts-window.ui',
  'sidebar.ui',
  'spinbutton.ui',
  'spinner.ui',
  'stack.ui',
  'stackswitcher.ui',
  'statusbar.ui',
  'switch.ui',
  'switch-state.ui',
  'toggle-button.ui',
  'video.ui',
  'volumebutton.ui',
  'window.ui',
  'windowcontrols.ui',
]

gtk_builder_tool = find_program('gtk4-builder-tool')

if get_option('screenshots')
  foreach ui_file: ui_files
    png_file = ui_file.replace('.ui', '.png')
    gtk_images += custom_target('@0@ from @1@'.format(png_file, ui_file),
                                input: [ui_file, 'style.css'],
                                output: png_file,
                                command: [ gtk_builder_tool, 'screenshot',
                                           '--force',
                                           '--css', '@INPUT1@',
                                           '@INPUT0@', '@OUTPUT@' ])

    dark_png_file = ui_file.replace('.ui', '-dark.png')
    gtk_images += custom_target('@0@ from @1@'.format(dark_png_file, ui_file),
                                input: [ui_file, 'style.css'],
                                output: dark_png_file,
                                env: [ 'GTK_THEME=Default-dark' ],
                                command: [ gtk_builder_tool, 'screenshot',
                                           '--force',
                                           '--css', '@INPUT1@',
                                           '@INPUT0@', '@OUTPUT@' ])
  endforeach
endif
