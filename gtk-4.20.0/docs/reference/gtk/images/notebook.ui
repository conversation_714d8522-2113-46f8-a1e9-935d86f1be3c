<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="decorated">0</property>
    <property name="resizable">0</property>
    <property name="default-width">280</property>
    <property name="default-height">120</property>
    <style>
       <class name="nobackground"/>
    </style>
    <child>
      <object class="GtkNotebook">
        <style>
          <class name="frame"/>
          <class name="shadow"/>
        </style>
        <property name="hexpand">1</property>
        <property name="vexpand">1</property>
        <child>
          <object class="GtkLabel">
            <property name="label">Content</property>
            <property name="halign">center</property>
            <property name="valign">center</property>
          </object>
        </child>
        <child type="tab">
          <object class="GtkLabel">
            <property name="label">Tab</property>
          </object>
        </child>
        <child>
          <object class="GtkLabel">
            <property name="label">Content</property>
            <property name="halign">center</property>
            <property name="valign">center</property>
          </object>
        </child>
        <child type="tab">
          <object class="GtkLabel">
            <property name="label">Tab</property>
          </object>
        </child>
        <child>
          <object class="GtkLabel">
            <property name="label">Content</property>
            <property name="halign">center</property>
            <property name="valign">center</property>
          </object>
        </child>
        <child type="tab">
          <object class="GtkLabel">
            <property name="label">Tab</property>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
