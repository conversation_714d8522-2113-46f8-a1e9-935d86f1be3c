<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="resizable">0</property>
    <property name="default-width">220</property>
    <property name="default-height">150</property>
    <property name="title">Header Bar</property>
    <child type='titlebar'>
      <object class="GtkHeaderBar">
        <child type='end'>
          <object class="GtkButton">
            <property name="icon-name">bookmark-new-symbolic</property>
          </object>
        </child>
      </object>
    </child>
    <child>
      <object class="GtkTextView">
        <property name="cursor-visible">0</property>
      </object>
    </child>
  </object>
</interface>
