<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="decorated">0</property>
    <property name="resizable">0</property>
    <property name="default-width">280</property>
    <property name="default-height">120</property>
    <style>
      <class name="nobackground"/>
    </style>
    <child>
      <object class="GtkBox">
        <style>
          <class name="shadow"/>
          <class name="background"/>
          <class name="frame"/>
        </style>
        <child>
          <object class="GtkBox">
            <property name="margin-top">10</property>
            <property name="margin-bottom">10</property>
            <property name="margin-start">20</property>
            <property name="margin-end">20</property>
            <property name="orientation">vertical</property>
            <property name="spacing">3</property>
            <child>
              <object class="GtkCenterBox">
                <property name="orientation">horizontal</property>
                <property name="hexpand">1</property>
                <property name="vexpand">1</property>
                <property name="halign">fill</property>
                <child type="start">
                  <object class="GtkButton">
                    <style><class name="small-button"/></style>
                    <property name="valign">center</property>
                  </object>
                </child>
                <child type="center">
                  <object class="GtkButton">
                    <style><class name="small-button"/></style>
                    <property name="valign">center</property>
                  </object>
                </child>
                <child type="end">
                  <object class="GtkButton">
                    <style><class name="small-button"/></style>
                    <property name="valign">center</property>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="justify">center</property>
                <property name="label">Center Box</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
