<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="decorated">0</property>
    <property name="resizable">0</property>
    <style>
      <class name="nobackground"/>
    </style>
    <child>
      <object class="GtkBox">
        <style>
          <class name="shadow"/>
          <class name="background"/>
          <class name="frame"/>
        </style>
        <child>
          <object class="GtkBox">
            <property name="margin-top">10</property>
            <property name="margin-bottom">10</property>
            <property name="margin-start">10</property>
            <property name="margin-end">10</property>
            <property name="orientation">vertical</property>
            <property name="spacing">3</property>
            <child>
              <object class="GtkBox">
                <property name="orientation">horizontal</property>
                <property name="homogeneous">1</property>
                <property name="spacing">12</property>

                <child>
                  <object class="GtkScale">
                    <property name="orientation">horizontal</property>
                    <property name="width-request">96</property>
                    <property name="height-request">96</property>
                    <property name="adjustment">
                      <object class="GtkAdjustment">
                        <property name="lower">0</property>
                        <property name="upper">100</property>
                        <property name="value">50</property>
                      </object>
                    </property>
                  </object>
                </child>

                <child>
                  <object class="GtkScale">
                    <property name="orientation">vertical</property>
                    <property name="width-request">96</property>
                    <property name="height-request">96</property>
                    <property name="adjustment">
                      <object class="GtkAdjustment">
                        <property name="lower">0</property>
                        <property name="upper">100</property>
                        <property name="value">50</property>
                      </object>
                    </property>
                  </object>
                </child>

              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="justify">center</property>
                <property name="label">Horizontal and Vertical
Scales</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
