<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="decorated">0</property>
    <property name="resizable">0</property>
    <property name="default-width">280</property>
    <style>
       <class name="nobackground"/>
    </style>
    <child>
      <object class="GtkBox">
        <style>
          <class name="frame"/>
          <class name="shadow"/>
          <class name="background"/>
        </style>
        <property name="orientation">vertical</property>
        <property name="spacing">3</property>
        <property name="hexpand">1</property>
        <property name="vexpand">1</property>
        <child>
          <object class="GtkFlowBox">
            <property name="min-children-per-line">2</property>
            <property name="max-children-per-line">2</property>
            <property name="selection-mode">browse</property>
            <child>
              <object class="GtkLabel">
                <property name="label">Child One</property>
              </object>
            </child>
            <child>
              <object class="GtkButton">
                <property name="label">Child Two</property>
              </object>
            </child>
            <child>
              <object class="GtkFlowBoxChild">
                <child>
                  <object class="GtkBox">
                    <property name="orientation">horizontal</property>
                    <property name="spacing">6</property>
                    <child>
                      <object class="GtkLabel">
                        <property name="label">Child Three</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkCheckButton">
                        <property name="active">1</property>
                      </object>
                    </child>
                  </object>
                </child>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkLabel">
            <property name="label">Flow Box</property>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
