<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="decorated">0</property>
    <property name="resizable">0</property>
    <property name="default-width">280</property>
    <property name="default-height">120</property>
    <style>
       <class name="nobackground"/>
    </style>
    <child>
      <object class="GtkBox">
        <style>
          <class name="shadow"/>
          <class name="background"/>
          <class name="frame"/>
        </style>
        <child>
          <object class="GtkInfoBar">
            <property name="hexpand">1</property>
            <property name="vexpand">1</property>
            <property name="halign">fill</property>
            <property name="valign">center</property>
            <property name="margin-start">10</property>
            <property name="margin-end">10</property>
            <property name="margin-top">10</property>
            <property name="margin-bottom">10</property>
            <property name="show-close-button">1</property>
            <property name="message-type">info</property>
            <child>
              <object class="GtkLabel">
                <property name="label">Info Bar</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
