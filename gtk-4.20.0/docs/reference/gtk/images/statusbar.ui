<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="decorated">0</property>
    <property name="resizable">0</property>
    <property name="default-width">280</property>
    <property name="default-height">120</property>
    <style>
       <class name="nobackground"/>
    </style>
    <child>
      <object class="GtkBox">
        <style>
           <class name="background"/>
           <class name="frame"/>
           <class name="shadow"/>
        </style>
        <property name="orientation">vertical</property>
        <property name="spacing">3</property>
        <child>
          <object class="GtkLabel">
            <property name="margin-top">10</property>
            <property name="label">Status Bar</property>
          </object>
        </child>
        <child>
          <object class="GtkStatusbar">
            <property name="hexpand">1</property>
            <property name="vexpand">1</property>
            <property name="valign">end</property>
            <child>
              <object class="GtkLabel">
                <property name="label">Hold on…</property>
                <property name="halign">start</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
