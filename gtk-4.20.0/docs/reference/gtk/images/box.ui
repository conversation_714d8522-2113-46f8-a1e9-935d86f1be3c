<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="decorated">0</property>
    <property name="resizable">0</property>
    <property name="default-width">280</property>
    <property name="default-height">120</property>
    <style>
      <class name="nobackground"/>
    </style>
    <child>
      <object class="GtkBox">
        <style>
          <class name="shadow"/>
          <class name="background"/>
          <class name="frame"/>
        </style>
        <child>
          <object class="GtkBox">
            <property name="margin-top">10</property>
            <property name="margin-bottom">10</property>
            <property name="margin-start">10</property>
            <property name="margin-end">10</property>
            <property name="orientation">vertical</property>
            <property name="spacing">3</property>
            <child>
              <object class="GtkBox">
                <property name="orientation">horizontal</property>
                <property name="homogeneous">1</property>

                <child>
                  <object class="GtkBox">
                    <property name="orientation">horizontal</property>
                    <property name="spacing">4</property>
                    <property name="halign">center</property>
                    <property name="valign">center</property>
                    <child>
                      <object class="GtkButton">
                        <style><class name="small-button"/></style>
                        <property name="valign">center</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton">
                        <style><class name="small-button"/></style>
                        <property name="valign">center</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="label">⋯</property>
                      </object>
                    </child>
                  </object>
                </child>

                <child>
                  <object class="GtkBox">
                    <property name="orientation">vertical</property>
                    <property name="spacing">4</property>
                    <property name="halign">center</property>
                    <property name="valign">center</property>
                    <child>
                      <object class="GtkButton">
                        <style><class name="small-button"/></style>
                        <property name="halign">center</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkButton">
                        <style><class name="small-button"/></style>
                        <property name="halign">center</property>
                      </object>
                    </child>
                    <child>
                      <object class="GtkLabel">
                        <property name="label">⋮</property>
                      </object>
                    </child>
                  </object>
                </child>

              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="justify">center</property>
                <property name="label">Horizontal and Vertical Boxes</property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
