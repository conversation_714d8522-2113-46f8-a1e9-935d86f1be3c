<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow">
    <property name="decorated">0</property>
    <property name="resizable">0</property>
    <property name="default-width">280</property>
    <property name="default-height">120</property>
    <style>
       <class name="nobackground"/>
    </style>
    <child>
      <object class="GtkBox">
        <style>
          <class name="shadow"/>
          <class name="background"/>
          <class name="frame"/>
        </style>
        <child>
          <object class="GtkBox">
            <property name="orientation">vertical</property>
            <property name="hexpand">1</property>
            <property name="vexpand">1</property>
            <child>
              <object class="GtkSearchBar">
                <property name="search-mode-enabled">1</property>
                <property name="show-close-button">1</property>
                <child>
                  <object class="GtkSearchEntry">
                    <property name="text">Search Bar</property>
                  </object>
                </child>
              </object>
            </child>
            <child>
              <object class="GtkTextView">
              </object>
            </child>
          </object>
        </child>
      </object>
    </child>
  </object>
</interface>
