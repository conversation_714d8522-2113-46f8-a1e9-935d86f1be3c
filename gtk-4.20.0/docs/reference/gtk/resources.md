Title: Contact information and bug reports
Slug: gtk-resources

## Opening a bug or feature request

If you encounter a bug, misfeature, or missing feature in GTK, please
file a bug report on our [GitLab project](https://gitlab.gnome.org/GNOME/gtk/issues/).
You should also file issues if the documentation is out of date with the
existing API, or unclear.

Don't hesitate to file a bug report, even if you think we may know
about it already, or aren't sure of the details. Just give us as much
information as you have, and if it's already fixed or has already been
discussed, we'll add a note to that effect in the report.

The issue tracker should definitely be used for feature requests, it's
not only for bugs. We track all GTK development in GitLab, to ensure
that nothing gets lost.

## Working on GTK

If you develop a bugfix or enhancement for GTK, please open a merge
request in GitLab as well. You should not attach patches to an issue,
or describe the fix as a comment. Merge requests allow us to build
GTK with your code applied, and run the test suite, on multiple platforms
and architectures, and verify that nothing breaks. They also allow us to
do proper code reviews, so we can iterate over the changes.

You should follow the [contribution guide](https://gitlab.gnome.org/GNOME/gtk/blob/main/CONTRIBUTING.md)
for GTK, available on GitLab.

If you want to discuss your approach before or after working on it,
good ways to contact the GTK developers, apart from GitLab issues,
are:

- the gtk tag on the [GNOME Discourse instance](https://discourse.gnome.org/tag/gtk)
- the `#gtk:gnome.org` Matrix room
- the `#gtk` IRC channel on `irc.libera.chat`

You should not send patches by email, as they will inevitably get lost,
or forgotten. Always open a merge request.
