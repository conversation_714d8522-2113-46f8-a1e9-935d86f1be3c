Title: Widget Gallery

<style>p { display: flex; flex-flow: row wrap; }</style>

## Display widgets

<a href="class.Label.html"><picture><source srcset="label-dark.png" media="(prefers-color-scheme: dark)"><img src="label.png"></picture></a>
<a href="class.Spinner.html"><picture><source srcset="spinner-dark.png" media="(prefers-color-scheme: dark)"><img src="spinner.png"></picture></a>
<a href="class.Statusbar.html"><picture><source srcset="statusbar-dark.png" media="(prefers-color-scheme: dark)"><img src="statusbar.png"></picture></a>
<a href="class.LevelBar.html"><picture><source srcset="levelbar-dark.png" media="(prefers-color-scheme: dark)"><img src="levelbar.png"></picture></a>
<a href="class.ProgressBar.html"><picture><source srcset="progressbar-dark.png" media="(prefers-color-scheme: dark)"><img src="progressbar.png"></picture></a>
<a href="class.InfoBar.html"><picture><source srcset="info-bar-dark.png" media="(prefers-color-scheme: dark)"><img src="info-bar.png"></picture></a>
<a href="class.Scrollbar.html"><picture><source srcset="scrollbar-dark.png" media="(prefers-color-scheme: dark)"><img src="scrollbar.png"></picture></a>
<a href="class.Image.html"><picture><source srcset="image-dark.png" media="(prefers-color-scheme: dark)"><img src="image.png"></picture></a>
<a href="class.Picture.html"><picture><source srcset="picture-dark.png" media="(prefers-color-scheme: dark)"><img src="picture.png"></picture></a>
<a href="class.Separator.html"><picture><source srcset="separator-dark.png" media="(prefers-color-scheme: dark)"><img src="separator.png"></picture></a>
<a href="class.TextView.html"><picture><source srcset="multiline-text-dark.png" media="(prefers-color-scheme: dark)"><img src="multiline-text.png"></picture></a>
<a href="class.Scale.html"><picture><source srcset="scales-dark.png" media="(prefers-color-scheme: dark)"><img src="scales.png"></picture></a>
<a href="class.GLArea.html"><picture><source srcset="glarea-dark.png" media="(prefers-color-scheme: dark)"><img src="glarea.png"></picture></a>
<a href="class.DrawingArea.html"><picture><source srcset="drawingarea-dark.png" media="(prefers-color-scheme: dark)"><img src="drawingarea.png"></picture></a>
<a href="class.Video.html"><picture><source srcset="video-dark.png" media="(prefers-color-scheme: dark)"><img src="video.png"></picture></a>
<a href="class.MediaControls.html"><picture><source srcset="media-controls-dark.png" media="(prefers-color-scheme: dark)"><img src="media-controls.png"></picture></a>
<a href="class.WindowControls.html"><picture><source srcset="windowcontrols-dark.png" media="(prefers-color-scheme: dark)"><img src="windowcontrols.png"></picture></a>
<a href="class.PopoverMenuBar.html"><picture><source srcset="menubar-dark.png" media="(prefers-color-scheme: dark)"><img src="menubar.png"></picture></a>
<a href="class.Calendar.html"><picture><source srcset="calendar-dark.png" media="(prefers-color-scheme: dark)"><img src="calendar.png"></picture></a>
<a href="class.EmojiChooser.html"><picture><source srcset="emojichooser-dark.png" media="(prefers-color-scheme: dark)"><img src="emojichooser.png"></picture></a>
<a href="class.PopoverMenu.html"><picture><source srcset="menu-dark.png" media="(prefers-color-scheme: dark)"><img src="menu.png"></picture></a>

## Buttons

<a href="class.Button.html"><picture><source srcset="button-dark.png" media="(prefers-color-scheme: dark)"><img src="button.png"></picture></a>
<a href="class.ToggleButton.html"><picture><source srcset="toggle-button-dark.png" media="(prefers-color-scheme: dark)"><img src="toggle-button.png"></picture></a>
<a href="class.LinkButton.html"><picture><source srcset="link-button-dark.png" media="(prefers-color-scheme: dark)"><img src="link-button.png"></picture></a>
<a href="class.CheckButton.html"><picture><source srcset="check-button-dark.png" media="(prefers-color-scheme: dark)"><img src="check-button.png"></picture></a>
<a href="class.CheckButton.html"><picture><source srcset="radio-button-dark.png" media="(prefers-color-scheme: dark)"><img src="radio-button.png"></picture></a>
<a href="class.MenuButton.html"><picture><source srcset="menu-button-dark.png" media="(prefers-color-scheme: dark)"><img src="menu-button.png"></picture></a>
<a href="class.LockButton.html"><picture><source srcset="lockbutton-dark.png" media="(prefers-color-scheme: dark)"><img src="lockbutton.png"></picture></a>
<a href="class.VolumeButton.html"><picture><source srcset="volumebutton-dark.png" media="(prefers-color-scheme: dark)"><img src="volumebutton.png"></picture></a>
<a href="class.Switch.html"><picture><source srcset="switch-dark.png" media="(prefers-color-scheme: dark)"><img src="switch.png"></picture></a>
<a href="class.ComboBox.html"><picture><source srcset="combo-box-dark.png" media="(prefers-color-scheme: dark)"><img src="combo-box.png"></picture></a>
<a href="class.ComboBoxText.html"><picture><source srcset="combo-box-text-dark.png" media="(prefers-color-scheme: dark)"><img src="combo-box-text.png"></picture></a>
<a href="class.DropDown.html"><picture><source srcset="drop-down-dark.png" media="(prefers-color-scheme: dark)"><img src="drop-down.png"></picture></a>
<a href="class.ColorDialogButton.html"><picture><source srcset="color-button-dark.png" media="(prefers-color-scheme: dark)"><img src="color-button.png"></picture></a>
<a href="class.FontDialogButton.html"><picture><source srcset="font-button-dark.png" media="(prefers-color-scheme: dark)"><img src="font-button.png"></picture></a>
<a href="class.AppChooserButton.html"><picture><source srcset="appchooserbutton-dark.png" media="(prefers-color-scheme: dark)"><img src="appchooserbutton.png"></picture></a>

## Entries

<a href="class.Entry.html"><picture><source srcset="entry-dark.png" media="(prefers-color-scheme: dark)"><img src="entry.png"></picture></a>
<a href="class.SearchEntry.html"><picture><source srcset="search-entry-dark.png" media="(prefers-color-scheme: dark)"><img src="search-entry.png"></picture></a>
<a href="class.PasswordEntry.html"><picture><source srcset="password-entry-dark.png" media="(prefers-color-scheme: dark)"><img src="password-entry.png"></picture></a>
<a href="class.SpinButton.html"><picture><source srcset="spinbutton-dark.png" media="(prefers-color-scheme: dark)"><img src="spinbutton.png"></picture></a>
<a href="class.EditableLabel.html"><picture><source srcset="editable-label-dark.png" media="(prefers-color-scheme: dark)"><img src="editable-label.png"></picture></a>

## Containers

<a href="class.Box.html"><picture><source srcset="box-dark.png" media="(prefers-color-scheme: dark)"><img src="box.png"></picture></a>
<a href="class.Grid.html"><picture><source srcset="grid-dark.png" media="(prefers-color-scheme: dark)"><img src="grid.png"></picture></a>
<a href="class.CenterBox.html"><picture><source srcset="centerbox-dark.png" media="(prefers-color-scheme: dark)"><img src="centerbox.png"></picture></a>
<a href="class.ScrolledWindow.html"><picture><source srcset="scrolledwindow-dark.png" media="(prefers-color-scheme: dark)"><img src="scrolledwindow.png"></picture></a>
<a href="class.Paned.html"><picture><source srcset="panes-dark.png" media="(prefers-color-scheme: dark)"><img src="panes.png"></picture></a>
<a href="class.Frame.html"><picture><source srcset="frame-dark.png" media="(prefers-color-scheme: dark)"><img src="frame.png"></picture></a>
<a href="class.Expander.html"><picture><source srcset="expander-dark.png" media="(prefers-color-scheme: dark)"><img src="expander.png"></picture></a>
<a href="class.SearchBar.html"><picture><source srcset="search-bar-dark.png" media="(prefers-color-scheme: dark)"><img src="search-bar.png"></picture></a>
<a href="class.ActionBar.html"><picture><source srcset="action-bar-dark.png" media="(prefers-color-scheme: dark)"><img src="action-bar.png"></picture></a>
<a href="class.HeaderBar.html"><picture><source srcset="headerbar-dark.png" media="(prefers-color-scheme: dark)"><img src="headerbar.png"></picture></a>
<a href="class.Notebook.html"><picture><source srcset="notebook-dark.png" media="(prefers-color-scheme: dark)"><img src="notebook.png"></picture></a>
<a href="class.ListBox.html"><picture><source srcset="list-box-dark.png" media="(prefers-color-scheme: dark)"><img src="list-box.png"></picture></a>
<a href="class.FlowBox.html"><picture><source srcset="flow-box-dark.png" media="(prefers-color-scheme: dark)"><img src="flow-box.png"></picture></a>
<a href="class.TreeView.html"><picture><source srcset="list-and-tree-dark.png" media="(prefers-color-scheme: dark)"><img src="list-and-tree.png"></picture></a>
<a href="class.IconView.html"><picture><source srcset="icon-view-dark.png" media="(prefers-color-scheme: dark)"><img src="icon-view.png"></picture></a>
<a href="class.Overlay.html"><picture><source srcset="overlay-dark.png" media="(prefers-color-scheme: dark)"><img src="overlay.png"></picture></a>
<a href="class.Stack.html"><picture><source srcset="stack-dark.png" media="(prefers-color-scheme: dark)"><img src="stack.png"></picture></a>
<a href="class.StackSwitcher.html"><picture><source srcset="stackswitcher-dark.png" media="(prefers-color-scheme: dark)"><img src="stackswitcher.png"></picture></a>
<a href="class.StackSidebar.html"><picture><source srcset="sidebar-dark.png" media="(prefers-color-scheme: dark)"><img src="sidebar.png"></picture></a>
<a href="class.Popover.html"><picture><source srcset="popover-dark.png" media="(prefers-color-scheme: dark)"><img src="popover.png"></picture></a>

## Windows

<a href="class.Window.html"><picture><source srcset="window-dark.png" media="(prefers-color-scheme: dark)"><img src="window.png"></picture></a>
<a href="class.Dialog.html"><picture><source srcset="dialog-dark.png" media="(prefers-color-scheme: dark)"><img src="dialog.png"></picture></a>
<a href="class.MessageDialog.html"><picture><source srcset="messagedialog-dark.png" media="(prefers-color-scheme: dark)"><img src="messagedialog.png"></picture></a>
<a href="class.AboutDialog.html"><picture><source srcset="aboutdialog-dark.png" media="(prefers-color-scheme: dark)"><img src="aboutdialog.png"></picture></a>
<a href="class.Assistant.html"><picture><source srcset="assistant-dark.png" media="(prefers-color-scheme: dark)"><img src="assistant.png"></picture></a>
<a href="class.ColorChooserDialog.html"><picture><source srcset="colorchooser-dark.png" media="(prefers-color-scheme: dark)"><img src="colorchooser.png"></picture></a>
<a href="class.FileChooserDialog.html"><picture><source srcset="filechooser-dark.png" media="(prefers-color-scheme: dark)"><img src="filechooser.png"></picture></a>
<a href="class.FontChooserDialog.html"><picture><source srcset="fontchooser-dark.png" media="(prefers-color-scheme: dark)"><img src="fontchooser.png"></picture></a>
<a href="class.AppChooserDialog.html"><picture><source srcset="appchooserdialog-dark.png" media="(prefers-color-scheme: dark)"><img src="appchooserdialog.png"></picture></a>
<a href="class.PageSetupUnixDialog.html"><picture><source srcset="pagesetupdialog-dark.png" media="(prefers-color-scheme: dark)"><img src="pagesetupdialog.png"></picture></a>
<a href="class.PrintUnixDialog.html"><picture><source srcset="printdialog-dark.png" media="(prefers-color-scheme: dark)"><img src="printdialog.png"></picture></a>
<a href="class.ShortcutsWindow.html"><picture><source srcset="shortcuts-window-dark.png" media="(prefers-color-scheme: dark)"><img src="shortcuts-window.png"></picture></a>
