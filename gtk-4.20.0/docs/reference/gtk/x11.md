Title: Using GTK with X11
Slug: gtk-x11

On UNIX, the X backend is enabled by default, so you don't need to do anything
special when compiling it, and everything should "just work."

To mix low-level Xlib routines into a GTK program, see
[GDK X Window System interaction](#gdk-X-Window-System-Interaction)
in the GDK manual.

## X11-specific environment variables
:
The X11 GDK backend can be influenced with some additional environment variables.

### `GDK_SYNCHRONIZE`

If set, GDK makes all X requests synchronously. This is a useful
option for debugging, but it will slow down the performance considerably.

### `GDK_SCALE`

Must be set to an integer, typically 2. If set, GDK will scale all
windows by the specified factor. Scaled output is meant to be used on
high-dpi displays. Normally, GDK will pick up a suitable scale factor
for each monitor from the display system. This environment variable
allows to override that.

## X11-specific APIs

See the [documentation](https://docs.gtk.org/gdk4-x11/) for
X11-specific GDK APIs.

## Understanding the X11 architecture

People coming from a Windows or MacOS background often find certain
aspects of the X Window System surprising. This section introduces
some basic X concepts at a high level. For more details, the book most
people use is called the "Xlib Programming Manual" by <PERSON>; this
book is volume one in the O'Reilly X Window System series.

Standards are another important resource if you're poking in low-level
X11 details, in particular the ICCCM and the Extended Window Manager
Hints specifications. [freedesktop.org](http://www.freedesktop.org/standards/)
has links to many relevant specifications.

The GDK manual covers [using Xlib in a GTK program](https://docs.gtk.org/gdk4/x11.html).

### Server, client, window manager

Other window systems typically put all their functionality in the
application itself. With X, each application involves three different
programs: the _X server_, the application (called a _client_ because
it's a client of the X server), and a special client called the
_window manager_.

The X server is in charge of managing resources, processing drawing
requests, and dispatching events such as keyboard and mouse events to
interested applications. So client applications can ask the X server
to create a window, draw a circle, or move windows around.

The window manager is in charge of rendering the frame or borders
around windows; it also has final say on the size of each window,
and window states such as minimized, maximized, and so forth.
On Windows and MacOS the application handles most of this.
On X11, if you wish to modify the window's state, or change its frame,
you must ask the window manager to do so on your behalf, using an
established  [convention](http://www.freedesktop.org/standards/).

GTK has functions for asking the window manager to do various things;
see for example [<EMAIL>] or [<EMAIL>].

Keep in mind that most window managers *will* ignore certain requests
from time to time, in the interests of good user interface.
