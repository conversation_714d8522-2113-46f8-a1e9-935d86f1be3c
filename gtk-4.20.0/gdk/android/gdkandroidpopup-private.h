/*
 * Copyright (c) 2024 Florian "sp1rit" <<EMAIL>>
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library. If not, see <http://www.gnu.org/licenses/>.
 *
 * SPDX-License-Identifier: LGPL-2.1-or-later
 */

#pragma once

#include "gdkpopupprivate.h"

#include "gdkandroidsurface-private.h"

#include "gdkandroidpopup.h"

G_BEGIN_DECLS

struct _GdkAndroidPopupClass
{
  GdkAndroidSurfaceClass parent_class;
};

struct _GdkAndroidPopup
{
  GdkAndroidSurface parent_instance;
  g<PERSON><PERSON> did_initial_present;

  GdkPopupLayout *layout;
  GdkRectangle popup_bounds;
};

G_END_DECLS
