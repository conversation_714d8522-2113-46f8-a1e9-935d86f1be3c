Cache surfaces that are opened via shm_open in between updates.
Handle implicit grabs when in broadway-server.c
keyboard focus handling
Add resize handling to js WM
Support window titles
_gdk_broadway_server_has_client is always FALSE, so resize don't work
Send reset events on client disconnect (button up, normal state. Maybe grab state reset?)
rgba support
shift-select in gedit doesn't work
backdrop mode
clean up /dev/shm on abrupt client exit
