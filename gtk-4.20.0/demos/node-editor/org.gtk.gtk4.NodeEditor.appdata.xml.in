<?xml version="1.0" encoding="UTF-8"?>
<component xmlns="https://specifications.freedesktop.org/metainfo/1.0" type="desktop">
  <id>org.gtk.gtk4.NodeEditor</id>
  <launchable type="desktop-id">org.gtk.gtk4.NodeEditor.desktop</launchable>
  <metadata_license>CC0-1.0</metadata_license>
  <project_license>LGPL-2.1-or-later</project_license>
  <name>GTK Node Editor</name>
  <summary>Program to edit render node files</summary>
  <description>
    <p>
      GTK Node Editor is a simple application to show and edit
      render node files.
    </p>
    <p>
      Render node files can e.g. be created by the GTK inspector.
    </p>
  </description>
  <screenshots>
    <screenshot>
      <image>https://static.gnome.org/appdata/gtk4-node-editor/gtk4-node-editor.png</image>
      <caption>Node Editor</caption>
    </screenshot>
  </screenshots>
  <kudos>
    <kudo>HiDpiIcon</kudo>
    <kudo>ModernToolkit</kudo>
  </kudos>
  <url type="homepage">https://www.gtk.org</url>
  <translation type="gettext">gtk40</translation>
  <update_contact>matthias.clasen_at_gmail.com</update_contact>
  <developer_name>Matthias Clasen and others</developer_name>
  <content_rating type="oars-1.1"/>
  <releases>
    <release version="@BUILD_VERSION@">
      <description>
        <p>A new build of GTK.</p>
      </description>
    </release>
  </releases>
</component>
