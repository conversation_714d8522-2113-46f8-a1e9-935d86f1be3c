<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkAdjustment" id="adjustment1">
    <property name="upper">4</property>
    <property name="value">2</property>
    <property name="step-increment">0.1</property>
    <property name="page-increment">1</property>
  </object>
  <object class="GtkAdjustment" id="adjustment2">
    <property name="upper">4</property>
    <property name="value">2</property>
    <property name="step-increment">0.1</property>
    <property name="page-increment">1</property>
  </object>
  <object class="GtkAdjustment" id="adjustment3">
    <property name="upper">4</property>
    <property name="value">2</property>
    <property name="step-increment">0.1</property>
    <property name="page-increment">1</property>
  </object>
  <object class="GtkWindow" id="window1">
    <property name="title" translatable="yes">Scales</property>
    <property name="resizable">0</property>
    <property name="child">
      <object class="GtkGrid" id="grid1">
        <property name="row-spacing">10</property>
        <property name="column-spacing">10</property>
        <property name="margin-start">20</property>
        <property name="margin-end">20</property>
        <property name="margin-top">20</property>
        <property name="margin-bottom">20</property>
        <child>
          <object class="GtkLabel" id="label_plain">
            <property name="label">Plain</property>
            <property name="xalign">0</property>
            <layout>
              <property name="column">0</property>
              <property name="row">0</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkScale" id="scale_plain">
            <property name="width-request">200</property>
            <property name="draw-value">0</property>
            <property name="adjustment">adjustment1</property>
            <property name="hexpand">1</property>
            <layout>
              <property name="column">1</property>
              <property name="row">0</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkLabel" id="label_marks">
            <property name="label">Marks</property>
            <property name="xalign">0</property>
            <layout>
              <property name="column">0</property>
              <property name="row">1</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkScale" id="scale_marks">
            <property name="width-request">200</property>
            <property name="draw-value">0</property>
            <property name="adjustment">adjustment2</property>
            <property name="hexpand">1</property>
            <marks>
              <mark value="0" position="bottom"></mark>
              <mark value="1" position="bottom"></mark>
              <mark value="2" position="bottom"></mark>
              <mark value="3" position="bottom"></mark>
              <mark value="4" position="bottom"></mark>
            </marks>
            <layout>
              <property name="column">1</property>
              <property name="row">1</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkLabel" id="label_discrete">
            <property name="label">Discrete</property>
            <property name="xalign">0</property>
            <layout>
              <property name="column">0</property>
              <property name="row">2</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkScale" id="scale_discrete">
            <property name="width-request">200</property>
            <property name="round-digits">0</property>
            <property name="draw-value">0</property>
            <property name="adjustment">adjustment3</property>
            <property name="hexpand">1</property>
            <marks>
              <mark value="0" position="bottom"></mark>
              <mark value="1" position="bottom"></mark>
              <mark value="2" position="bottom"></mark>
              <mark value="3" position="bottom"></mark>
              <mark value="4" position="bottom"></mark>
            </marks>
            <layout>
              <property name="column">1</property>
              <property name="row">2</property>
            </layout>
          </object>
        </child>
      </object>
    </property>
  </object>
</interface>
