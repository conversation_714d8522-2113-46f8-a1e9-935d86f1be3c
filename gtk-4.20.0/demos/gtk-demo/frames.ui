<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow" id="window">
    <property name="resizable">1</property>
    <property name="default-width">600</property>
    <property name="default-height">400</property>
    <property name="title">Frames</property>
    <property name="titlebar">
      <object class="GtkHeaderBar" id="header">
        <child type="end">
          <object class="GtkLabel" id="fps">
            <property name="attributes">0 -1 font-features "tnum=1"</property>
          </object>
        </child>
      </object>
    </property>
    <property name="child">
      <object class="GtkBox" id="box">
      </object>
    </property>
  </object>
</interface>
