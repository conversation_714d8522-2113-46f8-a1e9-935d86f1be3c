<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <template class="DemoApplicationWindow" parent="GtkApplicationWindow">
    <property name="title" translatable="yes">Application Class</property>
    <property name="default-width">200</property>
    <property name="default-height">200</property>
    <property name="icon-name">document-open</property>
    <property name="child">
      <object class="GtkGrid">
        <child>
          <object class="GtkBox">
            <property name="hexpand">1</property>
            <child>
              <object class="GtkMenuButton" id="menubutton">
                <property name="icon-name">document-open</property>
              </object>
            </child>
            <child>
              <object class="GtkButton">
                <property name="icon-name">application-exit</property>
                <property name="action-name">app.quit</property>
              </object>
            </child>
            <child>
              <object class="GtkSeparator"/>
            </child>
            <child>
              <object class="GtkButton">
                <property name="icon-name">applications-other</property>
                <property name="action-name">win.logo</property>
              </object>
            </child>
            <layout>
              <property name="column">0</property>
              <property name="row">0</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkInfoBar" id="infobar">
            <property name="visible">0</property>
            <property name="hexpand">1</property>
            <child>
              <object class="GtkLabel" id="message">
                <property name="hexpand">1</property>
              </object>
            </child>
            <child type="action">
              <object class="GtkButton">
                <property name="valign">center</property>
                <property name="label" translatable="yes">_OK</property>
                <property name="use-underline">1</property>
                <signal name="clicked" handler="clicked_cb"/>
              </object>
            </child>
            <layout>
              <property name="column">0</property>
              <property name="row">1</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkScrolledWindow">
            <property name="has-frame">1</property>
            <property name="child">
              <object class="GtkTextView">
                <property name="hexpand">1</property>
                <property name="vexpand">1</property>
                <property name="buffer">buffer</property>
              </object>
            </property>
            <layout>
              <property name="column">0</property>
              <property name="row">2</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkLabel" id="status">
            <property name="hexpand">1</property>
            <property name="xalign">0</property>
            <property name="margin-start">2</property>
            <property name="margin-end">2</property>
            <property name="margin-top">2</property>
            <property name="margin-bottom">2</property>
            <layout>
              <property name="column">0</property>
              <property name="row">3</property>
            </layout>
          </object>
        </child>
      </object>
    </property>
  </template>
  <menu id="toolmenu">
    <item>
      <attribute name="label">File1</attribute>
      <attribute name="action">win.file1</attribute>
    </item>
  </menu>
  <object class="GtkTextBuffer" id="buffer">
    <signal name="changed" handler="update_statusbar"/>
    <signal name="mark-set" handler="mark_set_callback"/>
  </object>
</interface>
