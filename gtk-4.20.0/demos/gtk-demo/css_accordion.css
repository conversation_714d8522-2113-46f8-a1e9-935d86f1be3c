.accordion, .accordion * {
    all: unset;

    transition-property: color, background-color, border-color, background-image, padding, border-width;
    transition-duration: 1s;

    font-size: 20px;
}

.accordion {
    background: linear-gradient(153deg, #151515, #151515 5px, transparent 5px) 0 0,
                linear-gradient(333deg, #151515, #151515 5px, transparent 5px) 10px 5px,
                linear-gradient(153deg, #222, #222 5px, transparent 5px) 0 5px,
                linear-gradient(333deg, #222, #222 5px, transparent 5px) 10px 10px,
                linear-gradient(90deg, #1b1b1b, #1b1b1b 10px, transparent 10px),
                linear-gradient(#1d1d1d, #1d1d1d 25%, #1a1a1a 25%, #1a1a1a 50%, transparent 50%, transparent 75%, #242424 75%, #242424);
    background-color: #131313;
    background-size: 20px 20px;
}

.accordion button {
    color: black;
    background-color: #bbb;
    border-style: solid;
    border-width: 2px 0 2px 2px;
    border-color: #333;

    padding: 12px 4px;
}

.accordion button:first-child {
    border-radius: 5px 0 0 5px;
}

.accordion button:last-child {
    border-radius: 0 5px 5px 0;
    border-width: 2px;
}

.accordion button:hover {
    padding: 12px 48px;
    background-color: #4870bc;
}

.accordion button *:hover {
    color: white;
}

.accordion button:hover:active,
.accordion button:active {
    background-color: #993401;
}
