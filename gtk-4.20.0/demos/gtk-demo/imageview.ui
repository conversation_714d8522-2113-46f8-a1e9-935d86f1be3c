<interface>
  <menu id="model">
    <item>
      <attribute name="label">Zoom Out</attribute>
      <attribute name="action">zoom.out</attribute>
    </item>
    <item>
      <attribute name="label">Zoom In</attribute>
      <attribute name="action">zoom.in</attribute>
    </item>
    <item>
      <attribute name="label">1∶1</attribute>
      <attribute name="action">zoom.reset</attribute>
    </item>
    <item>
      <attribute name="label">Rotate</attribute>
      <attribute name="action">rotate</attribute>
      <attribute name="target" type="i">90</attribute>
    </item>
  </menu>
  <template class="ImageView">
    <signal name="query-tooltip" handler="query_tooltip"/>
    <property name="has-tooltip">1</property>
    <accessibility>
      <property name="label">Image view</property>
    </accessibility>
    <child>
      <object class="GtkPopoverMenu" id="menu">
        <accessibility>
          <property name="label">Context menu</property>
        </accessibility>
        <property name="has-arrow">0</property>
        <property name="menu-model">model</property>
        <property name="halign">start</property>
      </object>
    </child>
    <child>
      <object class="GtkGestureClick">
	<property name="exclusive">true</property>
	<property name="button">3</property>
        <signal name="pressed" handler="pressed_cb"/>
      </object>
    </child>
    <child>
      <object class="GtkGestureClick">
	<property name="touch-only">true</property>
        <signal name="released" handler="pressed_cb"/>
      </object>
    </child>
    <child>
      <object class="GtkGestureZoom">
        <signal name="begin" handler="scale_begin_cb"/>
        <signal name="scale-changed" handler="scale_changed_cb"/>
      </object>
    </child>
    <child>
      <object class="GtkGestureRotate">
        <signal name="begin" handler="rotate_begin_cb"/>
        <signal name="angle-changed" handler="angle_changed_cb"/>
      </object>
    </child>
  </template>
</interface>
