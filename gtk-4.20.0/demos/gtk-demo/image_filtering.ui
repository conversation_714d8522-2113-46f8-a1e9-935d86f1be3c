<?xml version="1.0" encoding="UTF-8"?>
<interface domain="gtk40">
  <object class="GtkWindow" id="window">
    <property name="title" translatable="yes">Image Filtering</property>
    <property name="default-width">800</property>
    <property name="default-height">800</property>
    <property name="titlebar">
      <object class="GtkHeaderBar">
        <child type="start">
          <object class="GtkToggleButton" id="toggle">
            <property name="active">1</property>
            <property name="child">
              <object class="GtkImage">
                <property name="paintable">
                  <object class="GtkIconPaintable">
                    <property name="file">resource:///org/gtk/libgtk/icons/scalable/actions/open-menu-symbolic.svg</property>
                  </object>
                </property>
              </object>
            </property>
          </object>
        </child>
      </object>
    </property>
    <property name="child">
      <object class="GtkBox">
        <property name="orientation">horizontal</property>
        <child>
          <object class="GtkGrid">
            <property name="visible" bind-source="toggle" bind-property="active"/>
            <style><class name="sidebar"/></style>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Brightness</property>
                <layout>
                  <property name="column">0</property>
                  <property name="row">0</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkScale">
                <property name="adjustment">
                  <object class="GtkAdjustment" id="brightness">
                    <property name="lower">0</property>
                    <property name="upper">1</property>
                    <property name="value">1</property>
                    <property name="step-increment">0.1</property>
                  </object>
                </property>
                <layout>
                  <property name="column">1</property>
                  <property name="row">0</property>
                  <property name="column-span">2</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Contrast</property>
                <layout>
                  <property name="column">0</property>
                  <property name="row">1</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkScale">
                <property name="adjustment">
                  <object class="GtkAdjustment" id="contrast">
                    <property name="lower">0</property>
                    <property name="upper">1</property>
                    <property name="value">1</property>
                    <property name="step-increment">0.1</property>
                  </object>
                </property>
                <layout>
                  <property name="column">1</property>
                  <property name="row">1</property>
                  <property name="column-span">2</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Saturation</property>
                <layout>
                  <property name="column">0</property>
                  <property name="row">2</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkScale">
                <property name="adjustment">
                  <object class="GtkAdjustment" id="saturation">
                    <property name="lower">0</property>
                    <property name="upper">1</property>
                    <property name="value">1</property>
                    <property name="step-increment">0.1</property>
                  </object>
                </property>
                <layout>
                  <property name="column">1</property>
                  <property name="row">2</property>
                  <property name="column-span">2</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Sepia</property>
                <layout>
                  <property name="column">0</property>
                  <property name="row">3</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkScale">
                <property name="adjustment">
                  <object class="GtkAdjustment" id="sepia">
                    <property name="lower">0</property>
                    <property name="upper">1</property>
                    <property name="value">0</property>
                    <property name="step-increment">0.1</property>
                  </object>
                </property>
                <layout>
                  <property name="column">1</property>
                  <property name="row">3</property>
                  <property name="column-span">2</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Invert</property>
                <layout>
                  <property name="column">0</property>
                  <property name="row">4</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkScale">
                <property name="adjustment">
                  <object class="GtkAdjustment" id="invert">
                    <property name="lower">0</property>
                    <property name="upper">1</property>
                    <property name="value">0</property>
                    <property name="step-increment">0.1</property>
                  </object>
                </property>
                <layout>
                  <property name="column">1</property>
                  <property name="row">4</property>
                  <property name="column-span">2</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Rotate</property>
                <layout>
                  <property name="column">0</property>
                  <property name="row">5</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkScale">
                <property name="adjustment">
                  <object class="GtkAdjustment" id="rotate">
                    <property name="lower">0</property>
                    <property name="upper">360</property>
                    <property name="value">0</property>
                    <property name="step-increment">1</property>
                  </object>
                </property>
                <layout>
                  <property name="column">1</property>
                  <property name="row">5</property>
                  <property name="column-span">2</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Levels</property>
                <layout>
                  <property name="column">0</property>
                  <property name="row">6</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton">
                <property name="numeric">1</property>
                <property name="adjustment">
                  <object class="GtkAdjustment" id="levels">
                    <property name="lower">1</property>
                    <property name="upper">256</property>
                    <property name="value">256</property>
                    <property name="step-increment">1</property>
                  </object>
                </property>
                <layout>
                  <property name="column">1</property>
                  <property name="row">6</property>
                  <property name="column-span">1</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkLabel">
                <property name="label" translatable="yes">Linear</property>
                <layout>
                  <property name="column">0</property>
                  <property name="row">7</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton">
                <property name="numeric">1</property>
                <property name="digits">2</property>
                <property name="adjustment">
                  <object class="GtkAdjustment" id="linear_m">
                    <property name="lower">0</property>
                    <property name="upper">4</property>
                    <property name="value">1</property>
                    <property name="step-increment">0.1</property>
                  </object>
                </property>
                <layout>
                  <property name="column">1</property>
                  <property name="row">7</property>
                  <property name="column-span">1</property>
                </layout>
              </object>
            </child>
            <child>
              <object class="GtkSpinButton">
                <property name="numeric">1</property>
                <property name="digits">2</property>
                <property name="adjustment">
                  <object class="GtkAdjustment" id="linear_b">
                    <property name="lower">-1</property>
                    <property name="upper">1</property>
                    <property name="value">0</property>
                    <property name="step-increment">0.1</property>
                  </object>
                </property>
                <layout>
                  <property name="column">2</property>
                  <property name="row">7</property>
                  <property name="column-span">1</property>
                </layout>
              </object>
            </child>
          </object>
        </child>
        <child>
          <object class="GtkScrolledWindow">
            <child>
              <object class="GtkPicture">
                <property name="can-shrink">0</property>
                <property name="hexpand">1</property>
                <property name="paintable">
                  <object class="GtkFilterPaintable">
                    <property name="brightness" bind-source="brightness" bind-property="value"/>
                    <property name="contrast" bind-source="contrast" bind-property="value"/>
                    <property name="saturation" bind-source="saturation" bind-property="value"/>
                    <property name="sepia" bind-source="sepia" bind-property="value"/>
                    <property name="invert" bind-source="invert" bind-property="value"/>
                    <property name="rotate" bind-source="rotate" bind-property="value"/>
                    <property name="levels" bind-source="levels" bind-property="value"/>
                    <property name="linear-m" bind-source="linear_m" bind-property="value"/>
                    <property name="linear-b" bind-source="linear_b" bind-property="value"/>
                  </object>
                </property>
              </object>
            </child>
          </object>
        </child>
      </object>
    </property>
  </object>
</interface>
