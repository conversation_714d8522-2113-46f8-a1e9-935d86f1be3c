/*
 * First page.
 */
image.duck {
    background-image: url('resource://css_blendmodes/ducky.png');
    background-size: cover;
    min-width: 200px;
    min-height: 200px;
}

image.gradient {
    background-image: linear-gradient(to right, red 0%%, green 50%%, blue 100%%);
    min-width: 200px;
    min-height: 200px;
}

/*
 * Second page.
 */
image.red {
    background: url('resource://css_blendmodes/blends.png') top center;
    min-width: 200px;
    min-height: 200px;
}

image.blue {
    background: url('resource://css_blendmodes/blends.png') bottom center;
    min-width: 200px;
    min-height: 200px;
}

/*
 * Third page.
 */
image.cyan {
    background: url('resource://css_blendmodes/cmy.jpg') top center;
    min-width: 200px;
    min-height: 200px;
}

image.magenta {
    background: url('resource://css_blendmodes/cmy.jpg') center center;
    min-width: 200px;
    min-height: 200px;
}

image.yellow {
    background: url('resource://css_blendmodes/cmy.jpg') bottom center;
    min-width: 200px;
    min-height: 200px;
}

image.blend0 {
  background-image: url('resource://css_blendmodes/ducky.png'),
                    linear-gradient(to right, red 0%%, green 50%%, blue 100%%);
  background-size: cover;
  background-blend-mode: %s;
  min-width: 200px;
  min-height: 200px;
}

image.blend1 {
  background: url('resource://css_blendmodes/blends.png') top center,
              url('resource://css_blendmodes/blends.png') bottom center;
  background-blend-mode: %s;
  min-width: 200px;
  min-height: 200px;
}

image.blend2 {
  background: url('resource://css_blendmodes/cmy.jpg') top center,
              url('resource://css_blendmodes/cmy.jpg') center center,
              url('resource://css_blendmodes/cmy.jpg') bottom center;
  background-blend-mode: %s;
  min-width: 200px;
  min-height: 200px;
}
