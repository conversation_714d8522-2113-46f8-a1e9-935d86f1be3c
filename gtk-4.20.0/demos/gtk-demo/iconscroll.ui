<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow" id="window">
    <property name="resizable">1</property>
    <property name="default-width">500</property>
    <property name="default-height">500</property>
    <property name="titlebar">
      <object class="GtkHeaderBar">
        <child type="start">
          <object class="GtkBox">
            <style>
              <class name="linked"/>
            </style>
            <child>
              <object class="GtkButton">
                <property name="icon-name">go-previous-symbolic</property>
                <signal name="clicked" handler="iconscroll_prev_clicked_cb"/>
              </object>
            </child>
            <child>
              <object class="GtkButton">
                <property name="icon-name">go-next-symbolic</property>
                <signal name="clicked" handler="iconscroll_next_clicked_cb"/>
              </object>
            </child>
          </object>
        </child>
        <child type="end">
          <object class="GtkLabel" id="fps_label">
            <property name="attributes">0 -1 font-features "tnum=1"</property>
          </object>
        </child>
      </object>
    </property>
    <property name="child">
      <object class="GtkScrolledWindow" id="scrolledwindow">
        <property name="hscrollbar-policy">never</property>
        <property name="hadjustment">
          <object class="GtkAdjustment" id="hadjustment"/>
        </property>
        <property name="vadjustment">
          <object class="GtkAdjustment" id="vadjustment"/>
        </property>
      </object>
    </property>
  </object>
</interface>
