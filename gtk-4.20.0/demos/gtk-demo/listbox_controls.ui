<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow" id="window">
    <property name="title" translatable="yes">List Box — Controls</property>
    <property name="default-height">400</property>
    <property name="child">
      <object class="GtkScrolledWindow">
        <property name="hscrollbar-policy">never</property>
        <property name="min-content-height">200</property>
        <property name="hexpand">0</property>
        <property name="vexpand">1</property>
        <property name="child">
          <object class="GtkViewport">
            <property name="scroll-to-focus">1</property>
            <property name="child">
              <object class="GtkBox">
                <property name="orientation">vertical</property>
                <property name="margin-start">60</property>
                <property name="margin-end">60</property>
                <property name="margin-top">30</property>
                <property name="margin-bottom">30</property>
                <child>
                  <object class="GtkLabel">
                    <property name="label">Group 1</property>
                    <property name="xalign">0</property>
                    <property name="margin-bottom">10</property>
                    <style>
                      <class name="title-2"/>
                    </style>
                  </object>
                </child>
                <child>
                  <object class="GtkListBox">
                    <property name="selection-mode">none</property>
                    <signal name="row-activated" handler="row_activated"/>
                    <style>
                      <class name="rich-list"/>
                      <class name="boxed-list"/>
                    </style>

                    <child>
                      <object class="GtkListBoxRow">
                        <property name="selectable">0</property>
                        <child>
                          <object class="GtkBox">
                            <child>
                              <object class="GtkLabel" id="switch_label">
                                <property name="label" translatable="yes">Switch</property>
                                <property name="xalign">0</property>
                                <property name="halign">start</property>
                                <property name="valign">center</property>
                                <property name="hexpand">1</property>
                                <property name="mnemonic-widget">switch</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkSwitch" id="switch">
                                <property name="halign">end</property>
                                <property name="valign">center</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>

                    <child>
                      <object class="GtkListBoxRow">
                        <property name="selectable">0</property>
                        <child>
                          <object class="GtkBox">
                            <child>
                              <object class="GtkLabel" id="check_label">
                                <property name="label" translatable="yes">Check</property>
                                <property name="xalign">0</property>
                                <property name="halign">start</property>
                                <property name="valign">center</property>
                                <property name="hexpand">1</property>
                                <property name="mnemonic-widget">check</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkCheckButton" id="check">
                                <property name="halign">end</property>
                                <property name="valign">center</property>
                                <property name="margin-start">10</property>
                                <property name="margin-end">10</property>
                                <property name="active">1</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>

                    <child>
                      <object class="GtkListBoxRow">
                        <property name="selectable">0</property>
                        <child>
                          <object class="GtkBox">
                            <child>
                              <object class="GtkLabel" id="image_label">
                                <property name="label" translatable="yes">Click here!</property>
                                <property name="xalign">0</property>
                                <property name="halign">start</property>
                                <property name="valign">center</property>
                                <property name="hexpand">1</property>
                              </object>
                            </child>
                            <child>
                              <object class="GtkImage" id="image">
                                <property name="icon-name">object-select-symbolic</property>
                                <property name="halign">end</property>
                                <property name="valign">center</property>
                                <property name="margin-start">10</property>
                                <property name="margin-end">10</property>
                                <property name="opacity">0</property>
                                <property name="accessible-role">status</property>
                              </object>
                            </child>
                          </object>
                        </child>
                      </object>
                    </child>
                  </object>
                </child>

                <child>
                  <object class="GtkLabel">
                    <property name="margin-top">30</property>
                    <property name="margin-bottom">10</property>
                    <property name="label">Group 2</property>
                    <property name="xalign">0</property>
                    <style>
                      <class name="title-2"/>
                    </style>
                  </object>
                </child>
                  <child>
                    <object class="GtkListBox">
                      <property name="selection-mode">none</property>
                      <style>
                        <class name="rich-list"/>
                        <class name="boxed-list"/>
                      </style>

                      <child>
                        <object class="GtkListBoxRow">
                          <property name="selectable">0</property>
                          <property name="activatable">0</property>
                          <child>
                            <object class="GtkBox">
                              <child>
                                <object class="GtkLabel" id="scale_label">
                                  <property name="label" translatable="yes">Scale</property>
                                  <property name="xalign">0</property>
                                  <property name="halign">start</property>
                                  <property name="valign">center</property>
                                  <property name="hexpand">1</property>
                                  <property name="mnemonic-widget">scale</property>
                                </object>
                              </child>
                              <child>
                                <object class="GtkScale" id="scale">
                                  <property name="halign">end</property>
                                  <property name="valign">center</property>
                                  <property name="draw-value">0</property>
                                  <property name="width-request">150</property>
                                  <property name="adjustment">
                                    <object class="GtkAdjustment">
                                      <property name="upper">100</property>
                                      <property name="value">50</property>
                                      <property name="step-increment">1</property>
                                      <property name="page-increment">10</property>
                                    </object>
                                  </property>
                                </object>
                              </child>
                            </object>
                          </child>
                        </object>
                      </child>

                      <child>
                        <object class="GtkListBoxRow">
                          <property name="selectable">0</property>
                          <property name="activatable">0</property>
                          <child>
                            <object class="GtkBox">
                              <child>
                                <object class="GtkLabel" id="spin_label">
                                  <property name="label" translatable="yes">Spinbutton</property>
                                  <property name="xalign">0</property>
                                  <property name="halign">start</property>
                                  <property name="valign">center</property>
                                  <property name="hexpand">1</property>
                                  <property name="mnemonic-widget">spin</property>
                                </object>
                              </child>
                              <child>
                                <object class="GtkSpinButton" id="spin">
                                  <property name="halign">end</property>
                                  <property name="valign">center</property>
                                  <property name="adjustment">
                                    <object class="GtkAdjustment">
                                      <property name="upper">100</property>
                                      <property name="value">50</property>
                                      <property name="step-increment">1</property>
                                      <property name="page-increment">10</property>
                                    </object>
                                  </property>
                                </object>
                              </child>
                            </object>
                          </child>
                        </object>
                      </child>
                      <child>
                        <object class="GtkListBoxRow">
                          <property name="selectable">0</property>
                          <property name="activatable">0</property>
                          <child>
                            <object class="GtkBox">
                              <child>
                                <object class="GtkLabel" id="dropdown_label">
                                  <property name="label" translatable="yes">Dropdown</property>
                                  <property name="xalign">0</property>
                                  <property name="halign">start</property>
                                  <property name="valign">center</property>
                                  <property name="hexpand">1</property>
                                  <property name="mnemonic-widget">dropdown</property>
                                </object>
                              </child>
                              <child>
                                <object class="GtkDropDown" id="dropdown">
                                  <property name="halign">end</property>
                                  <property name="valign">center</property>
                                  <property name="model">
                                    <object class="GtkStringList">
                                      <property name="strings">Choice 1
Choice 2
Choice 3
Choice 4</property>
                                    </object>
                                  </property>
                                </object>
                              </child>
                            </object>
                          </child>
                        </object>
                      </child>

                      <child>
                        <object class="GtkListBoxRow">
                          <property name="selectable">0</property>
                          <property name="activatable">0</property>
                          <child>
                            <object class="GtkBox">
                              <child>
                                <object class="GtkLabel" id="entry_label">
                                  <property name="label" translatable="yes">Entry</property>
                                  <property name="xalign">0</property>
                                  <property name="halign">start</property>
                                  <property name="valign">center</property>
                                  <property name="hexpand">1</property>
                                  <property name="mnemonic-widget">entry</property>
                                </object>
                              </child>
                              <child>
                                <object class="GtkEntry" id="entry">
                                  <property name="halign">end</property>
                                  <property name="valign">center</property>
                                  <property name="placeholder-text">Type here…</property>
                                </object>
                              </child>
                            </object>
                          </child>
                        </object>
                      </child>

                  </object>
                </child>
              </object>
            </property>
          </object>
        </property>
      </object>
    </property>
  </object>
  <object class="GtkSizeGroup">
    <property name="mode">horizontal</property>
    <widgets>
      <widget name="switch_label"/>
      <widget name="check_label"/>
      <widget name="image_label"/>
      <widget name="scale_label"/>
      <widget name="spin_label"/>
      <widget name="dropdown_label"/>
      <widget name="entry_label"/>
    </widgets>
  </object>
</interface>
