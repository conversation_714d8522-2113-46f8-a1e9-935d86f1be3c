/* You can edit the text in this window to change the
 * appearance of this Window.
 * Be careful, if you screw it up, nothing might be visible
 * anymore. :)
 */

/* This CSS resets all properties to their defaults values
 *    and overrides all user settings and the theme in use */
@import url("resource://css_pixbufs/reset.css");
@import url("resource://css_pixbufs/cssview.css");

@keyframes move-the-image {
0% { background-position: 50.00% 75.00%, 67.68% 67.68%, 75.00% 50.00%, 67.68% 32.32%, 50.00% 25.00%, 32.32% 32.32%, 25.00% 50.00%, 32.32% 67.68%, 0% 0%; }
3.125% { background-position: 55.19% 76.11%, 72.14% 64.79%, 76.11% 44.81%, 64.79% 27.86%, 44.81% 23.89%, 27.86% 35.21%, 23.89% 55.19%, 35.21% 72.14%, 0% 0%; }
6.25% { background-position: 60.79% 76.04%, 76.04% 60.79%, 76.04% 39.21%, 60.79% 23.96%, 39.21% 23.96%, 23.96% 39.21%, 23.96% 60.79%, 39.21% 76.04%, 0% 0%; }
9.375% { background-position: 66.46% 74.64%, 79.06% 55.78%, 74.64% 33.54%, 55.78% 20.94%, 33.54% 25.36%, 20.94% 44.22%, 25.36% 66.46%, 44.22% 79.06%, 0% 0%; }
12.5% { background-position: 71.84% 71.84%, 80.89% 50.00%, 71.84% 28.16%, 50.00% 19.11%, 28.16% 28.16%, 19.11% 50.00%, 28.16% 71.84%, 50.00% 80.89%, 0% 0%; }
15.625% { background-position: 76.55% 67.74%, 81.32% 43.77%, 67.74% 23.45%, 43.77% 18.68%, 23.45% 32.26%, 18.68% 56.23%, 32.26% 76.55%, 56.23% 81.32%, 0% 0%; }
18.75% { background-position: 80.21% 62.51%, 80.21% 37.49%, 62.51% 19.79%, 37.49% 19.79%, 19.79% 37.49%, 19.79% 62.51%, 37.49% 80.21%, 62.51% 80.21%, 0% 0%; }
21.875% { background-position: 82.54% 56.47%, 77.58% 31.57%, 56.47% 17.46%, 31.57% 22.42%, 17.46% 43.53%, 22.42% 68.43%, 43.53% 82.54%, 68.43% 77.58%, 0% 0%; }
25% { background-position: 83.33% 50.00%, 73.57% 26.43%, 50.00% 16.67%, 26.43% 26.43%, 16.67% 50.00%, 26.43% 73.57%, 50.00% 83.33%, 73.57% 73.57%, 0% 0%; }
28.125% { background-position: 82.54% 43.53%, 68.43% 22.42%, 43.53% 17.46%, 22.42% 31.57%, 17.46% 56.47%, 31.57% 77.58%, 56.47% 82.54%, 77.58% 68.43%, 0% 0%; }
31.25% { background-position: 80.21% 37.49%, 62.51% 19.79%, 37.49% 19.79%, 19.79% 37.49%, 19.79% 62.51%, 37.49% 80.21%, 62.51% 80.21%, 80.21% 62.51%, 0% 0%; }
34.375% { background-position: 76.55% 32.26%, 56.23% 18.68%, 32.26% 23.45%, 18.68% 43.77%, 23.45% 67.74%, 43.77% 81.32%, 67.74% 76.55%, 81.32% 56.23%, 0% 0%; }
37.5% { background-position: 71.84% 28.16%, 50.00% 19.11%, 28.16% 28.16%, 19.11% 50.00%, 28.16% 71.84%, 50.00% 80.89%, 71.84% 71.84%, 80.89% 50.00%, 0% 0%; }
40.625% { background-position: 66.46% 25.36%, 44.22% 20.94%, 25.36% 33.54%, 20.94% 55.78%, 33.54% 74.64%, 55.78% 79.06%, 74.64% 66.46%, 79.06% 44.22%, 0% 0%; }
43.75% { background-position: 60.79% 23.96%, 39.21% 23.96%, 23.96% 39.21%, 23.96% 60.79%, 39.21% 76.04%, 60.79% 76.04%, 76.04% 60.79%, 76.04% 39.21%, 0% 0%; }
46.875% { background-position: 55.19% 23.89%, 35.21% 27.86%, 23.89% 44.81%, 27.86% 64.79%, 44.81% 76.11%, 64.79% 72.14%, 76.11% 55.19%, 72.14% 35.21%, 0% 0%; }
50% { background-position: 50.00% 25.00%, 32.32% 32.32%, 25.00% 50.00%, 32.32% 67.68%, 50.00% 75.00%, 67.68% 67.68%, 75.00% 50.00%, 67.68% 32.32%, 0% 0%; }
53.125% { background-position: 45.44% 27.07%, 30.57% 37.01%, 27.07% 54.56%, 37.01% 69.43%, 54.56% 72.93%, 69.43% 62.99%, 72.93% 45.44%, 62.99% 30.57%, 0% 0%; }
56.25% { background-position: 41.65% 29.85%, 29.85% 41.65%, 29.85% 58.35%, 41.65% 70.15%, 58.35% 70.15%, 70.15% 58.35%, 70.15% 41.65%, 58.35% 29.85%, 0% 0%; }
59.375% { background-position: 38.68% 33.06%, 30.02% 46.03%, 33.06% 61.32%, 46.03% 69.98%, 61.32% 66.94%, 69.98% 53.97%, 66.94% 38.68%, 53.97% 30.02%, 0% 0%; }
62.5% { background-position: 36.49% 36.49%, 30.89% 50.00%, 36.49% 63.51%, 50.00% 69.11%, 63.51% 63.51%, 69.11% 50.00%, 63.51% 36.49%, 50.00% 30.89%, 0% 0%; }
65.625% { background-position: 34.97% 39.96%, 32.28% 53.53%, 39.96% 65.03%, 53.53% 67.72%, 65.03% 60.04%, 67.72% 46.47%, 60.04% 34.97%, 46.47% 32.28%, 0% 0%; }
68.75% { background-position: 34.02% 43.38%, 34.02% 56.62%, 43.38% 65.98%, 56.62% 65.98%, 65.98% 56.62%, 65.98% 43.38%, 56.62% 34.02%, 43.38% 34.02%, 0% 0%; }
71.875% { background-position: 33.50% 46.72%, 36.01% 59.35%, 46.72% 66.50%, 59.35% 63.99%, 66.50% 53.28%, 63.99% 40.65%, 53.28% 33.50%, 40.65% 36.01%, 0% 0%; }
75% { background-position: 33.33% 50.00%, 38.21% 61.79%, 50.00% 66.67%, 61.79% 61.79%, 66.67% 50.00%, 61.79% 38.21%, 50.00% 33.33%, 38.21% 38.21%, 0% 0%; }
78.125% { background-position: 33.50% 53.28%, 40.65% 63.99%, 53.28% 66.50%, 63.99% 59.35%, 66.50% 46.72%, 59.35% 36.01%, 46.72% 33.50%, 36.01% 40.65%, 0% 0%; }
81.25% { background-position: 34.02% 56.62%, 43.38% 65.98%, 56.62% 65.98%, 65.98% 56.62%, 65.98% 43.38%, 56.62% 34.02%, 43.38% 34.02%, 34.02% 43.38%, 0% 0%; }
84.375% { background-position: 34.97% 60.04%, 46.47% 67.72%, 60.04% 65.03%, 67.72% 53.53%, 65.03% 39.96%, 53.53% 32.28%, 39.96% 34.97%, 32.28% 46.47%, 0% 0%; }
87.5% { background-position: 36.49% 63.51%, 50.00% 69.11%, 63.51% 63.51%, 69.11% 50.00%, 63.51% 36.49%, 50.00% 30.89%, 36.49% 36.49%, 30.89% 50.00%, 0% 0%; }
90.625% { background-position: 38.68% 66.94%, 53.97% 69.98%, 66.94% 61.32%, 69.98% 46.03%, 61.32% 33.06%, 46.03% 30.02%, 33.06% 38.68%, 30.02% 53.97%, 0% 0%; }
93.75% { background-position: 41.65% 70.15%, 58.35% 70.15%, 70.15% 58.35%, 70.15% 41.65%, 58.35% 29.85%, 41.65% 29.85%, 29.85% 41.65%, 29.85% 58.35%, 0% 0%; }
96.875% { background-position: 45.44% 72.93%, 62.99% 69.43%, 72.93% 54.56%, 69.43% 37.01%, 54.56% 27.07%, 37.01% 30.57%, 27.07% 45.44%, 30.57% 62.99%, 0% 0%; }
100% { background-position: 50.00% 75.00%, 67.68% 67.68%, 75.00% 50.00%, 67.68% 32.32%, 50.00% 25.00%, 32.32% 32.32%, 25.00% 50.00%, 32.32% 67.68%, 0% 0%; }
}

@keyframes size-the-image {
    0% { background-size: 96px, 12px, 96px, 12px, 96px, 12px, 96px, 12px, auto; }
    100% { background-size: 12px, 96px, 12px, 96px, 12px, 96px, 12px, 96px, auto; }
}

window.demo {
    background-image: url("resource://css_pixbufs/images/apple-red.png"),
                      url("resource://css_pixbufs/images/gnome-applets.png"),
                      url("resource://css_pixbufs/images/gnome-calendar.png"),
                      url("resource://css_pixbufs/images/gnome-foot.png"),
                      url("resource://css_pixbufs/images/gnome-gmush.png"),
                      url("resource://css_pixbufs/images/gnome-gimp.png"),
                      url("resource://css_pixbufs/images/gnome-gsame.png"),
                      url("resource://css_pixbufs/images/gnu-keys.png"),
                      url("resource://css_pixbufs/background.jpg");
    background-position: 50.00% 75.00%, 67.68% 67.68%, 75.00% 50.00%, 67.68% 32.32%, 50.00% 25.00%, 32.32% 32.32%, 25.00% 50.00%, 32.32% 67.68%, 0% 0%;
    background-repeat: no-repeat, no-repeat, no-repeat, no-repeat, no-repeat, no-repeat, no-repeat, no-repeat, repeat;
    animation: move-the-image infinite linear 3s, size-the-image infinite alternate ease-in-out 0.75s;
}

/* Make the text editor has a nice style */
window.demo .view, scrollbar, separator {
  color: black;
  background-color: rgba(255,255,255,0.5);
}

window.demo .view:selected {
  background-color: rgba(127,127,255,0.5);
}
