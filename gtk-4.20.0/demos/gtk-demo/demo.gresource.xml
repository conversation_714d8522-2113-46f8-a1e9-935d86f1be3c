<?xml version="1.0" encoding="UTF-8"?>
<gresources>
  <gresource prefix="/">
  </gresource>
  <gresource prefix="/ui">
    <file preprocess="xml-stripblanks">main.ui</file>
    <file preprocess="xml-stripblanks">main-listitem.ui</file>
  </gresource>
  <gresource prefix="/application_demo">
    <file>application.c</file>
    <file>application.ui</file>
    <file>menus.ui</file>
  </gresource>
  <gresource prefix="/builder">
    <file>demo.ui</file>
  </gresource>
  <gresource prefix="/clipboard">
    <file>clipboard.ui</file>
    <file>demoimage.c</file>
    <file>demoimage.h</file>
  </gresource>
  <gresource prefix="/constraints_builder">
    <file>constraints_builder.ui</file>
  </gresource>
  <gresource prefix="/css_accordion">
    <file>css_accordion.css</file>
  </gresource>
  <gresource prefix="/css_basics">
    <file>css_basics.css</file>
    <file>reset.css</file>
  </gresource>
  <gresource prefix="/css_blendmodes">
    <file>css_blendmodes.css</file>
    <file>blendmodes.ui</file>
    <file>blends.png</file>
    <file>ducky.png</file>
    <file>cmy.jpg</file>
  </gresource>
  <gresource prefix="/css_multiplebgs">
    <file>css_multiplebgs.css</file>
    <file>brick.png</file>
    <file>brick2.png</file>
    <file>cssview.css</file>
    <file>reset.css</file>
  </gresource>
  <gresource prefix="/listview_selections">
    <file>suggestionentry.h</file>
    <file>suggestionentry.c</file>
    <file>suggestionentry.css</file>
  </gresource>
  <gresource prefix="/theming_style_classes">
    <file>theming.ui</file>
  </gresource>
  <gresource prefix="/themes">
    <file>themes.ui</file>
  </gresource>
  <gresource prefix="/css_pixbufs">
    <file alias="gtk.css">css_pixbufs.css</file>
    <file>cssview.css</file>
    <file>reset.css</file>
    <file>background.jpg</file>
  </gresource>
  <gresource prefix="/css_pixbufs/images">
    <file>apple-red.png</file>
    <file>gnome-applets.png</file>
    <file>gnome-calendar.png</file>
    <file>gnome-foot.png</file>
    <file>gnome-gmush.png</file>
    <file>gnome-gimp.png</file>
    <file>gnome-gsame.png</file>
    <file>gnu-keys.png</file>
  </gresource>
  <gresource prefix="/css_shadows">
    <file alias="gtk.css">css_shadows.css</file>
    <file>cssview.css</file>
    <file>reset.css</file>
  </gresource>
  <gresource prefix="/cursors">
    <file>cursors.ui</file>
    <file>cursors.css</file>
  </gresource>
  <gresource prefix="/cursors/images">
    <file>alias_cursor.png</file>
    <file>all_scroll_cursor.png</file>
    <file>cell_cursor.png</file>
    <file>col_resize_cursor.png</file>
    <file>context_menu_cursor.png</file>
    <file>copy_cursor.png</file>
    <file>crosshair_cursor.png</file>
    <file>default_cursor.png</file>
    <file>e_resize_cursor.png</file>
    <file>ew_resize_cursor.png</file>
    <file>gtk_logo_cursor.png</file>
    <file>grabbing_cursor.png</file>
    <file>grab_cursor.png</file>
    <file>hand_cursor.png</file>
    <file>help_cursor.png</file>
    <file>move_cursor.png</file>
    <file>ne_resize_cursor.png</file>
    <file>nesw_resize_cursor.png</file>
    <file>no_drop_cursor.png</file>
    <file>none_cursor.png</file>
    <file>not_allowed_cursor.png</file>
    <file>n_resize_cursor.png</file>
    <file>ns_resize_cursor.png</file>
    <file>nw_resize_cursor.png</file>
    <file>nwse_resize_cursor.png</file>
    <file>pointer_cursor.png</file>
    <file>progress_cursor.png</file>
    <file>row_resize_cursor.png</file>
    <file>se_resize_cursor.png</file>
    <file>s_resize_cursor.png</file>
    <file>sw_resize_cursor.png</file>
    <file>text_cursor.png</file>
    <file>vertical_text_cursor.png</file>
    <file>wait_cursor.png</file>
    <file>w_resize_cursor.png</file>
    <file>zoom_in_cursor.png</file>
    <file>zoom_out_cursor.png</file>
    <file>gtk-logo.svg</file>
  </gresource>
  <gresource prefix="/dnd">
    <file>dnd.css</file>
  </gresource>
  <gresource prefix="/errorstates">
    <file>errorstates.ui</file>
  </gresource>
  <gresource prefix="/fishbowl">
    <file>fishbowl.ui</file>
    <file>gtkfishbowl.c</file>
    <file>gtkfishbowl.h</file>
    <file>tiger.node</file>
  </gresource>
  <gresource prefix="/frames">
    <file>frames.ui</file>
  </gresource>
  <gresource prefix="/gears">
    <file>gtkgears.c</file>
    <file>gtkgears.h</file>
  </gresource>
  <gresource prefix="/shadertoy">
    <file>gtkshadertoy.c</file>
    <file>gtkshadertoy.h</file>
    <file>alienplanet.glsl</file>
    <file>mandelbrot.glsl</file>
    <file>neon.glsl</file>
    <file>cogs.glsl</file>
    <file>glowingstars.glsl</file>
  </gresource>
  <gresource prefix="/iconscroll">
    <file>iconscroll.ui</file>
  </gresource>
  <gresource prefix="/iconview">
    <file>gnome-fs-directory.png</file>
    <file>gnome-fs-regular.png</file>
  </gresource>
  <gresource prefix="/layoutmanager">
    <file>demolayout.h</file>
    <file>demolayout.c</file>
    <file>demowidget.h</file>
    <file>demowidget.c</file>
    <file>demochild.h</file>
    <file>demochild.c</file>
  </gresource>
  <gresource prefix="/layoutmanager2">
    <file>demo2layout.h</file>
    <file>demo2layout.c</file>
    <file>demo2widget.h</file>
    <file>demo2widget.c</file>
    <file>four_point_transform.h</file>
    <file>four_point_transform.c</file>
    <file>singular_value_decomposition.h</file>
    <file>singular_value_decomposition.c</file>
  </gresource>
  <gresource prefix="/listview_filebrowser">
    <file>listview_filebrowser.ui</file>
    <file>listview_filebrowser.css</file>
  </gresource>
  <gresource prefix="/listview_minesweeper">
    <file>listview_minesweeper.ui</file>
    <file>listview_minesweeper_cell.ui</file>
  </gresource>
  <gresource prefix="/listview_settings">
    <file>listview_settings.ui</file>
  </gresource>
  <gresource prefix="/listview_settings2">
    <file>listview_settings2.ui</file>
  </gresource>
  <gresource prefix="/listview_ucd_data/">
    <file>ucdnames.data</file>
  </gresource>
  <gresource prefix="/listview_weather">
    <file compressed="true">listview_weather.txt</file>
  </gresource>
  <gresource prefix="/listview_colors">
    <file compressed="true">color.names.txt</file>
    <file>listview_colors.css</file>
  </gresource>
  <gresource prefix="/main">
    <file>fontify.c</file>
    <file>fontify.h</file>
    <file>main.ui</file>
  </gresource>
  <gresource prefix="/menu">
    <file>imageview.c</file>
    <file>imageview.h</file>
    <file>imageview.ui</file>
  </gresource>
  <gresource prefix="/mask">
    <file>demo4widget.c</file>
    <file>demo4widget.h</file>
    <file>hsla.h</file>
    <file>hsla.c</file>
  </gresource>
  <gresource prefix="/paintable_svg">
    <file>svgpaintable.h</file>
    <file>svgpaintable.c</file>
    <file>symbolicpaintable.h</file>
    <file>symbolicpaintable.c</file>
    <file>org.gtk.gtk4.NodeEditor.Devel.svg</file>
  </gresource>
  <gresource prefix="/shortcuts">
    <file>shortcuts.ui</file>
    <file>shortcuts-builder.ui</file>
    <file>shortcuts-gedit.ui</file>
    <file>shortcuts-clocks.ui</file>
    <file>shortcuts-boxes.ui</file>
  </gresource>
  <gresource prefix="/sliding_puzzle">
    <file>puzzlepiece.c</file>
    <file>puzzlepiece.h</file>
    <file>portland-rose.jpg</file>
  </gresource>
  <gresource prefix="/stack">
    <file>stack.ui</file>
  </gresource>
  <gresource prefix="/revealer">
    <file>revealer.ui</file>
  </gresource>
  <gresource prefix="/images">
    <file>pixbufpaintable.h</file>
    <file>pixbufpaintable.c</file>
    <file>alphatest.png</file>
    <file>floppybuddy.gif</file>
    <file>gtk-logo.webm</file>
    <file alias="org.gtk.Demo4.svg">data/scalable/apps/org.gtk.Demo4.svg</file>
  </gresource>
  <gresource prefix="/video-player">
    <file>bbb.png</file>
  </gresource>
  <gresource prefix="/sources">
    <file>application_demo.c</file>
    <file>assistant.c</file>
    <file>builder.c</file>
    <file>clipboard.c</file>
    <file>combobox.c</file>
    <file>constraints.c</file>
    <file>constraints_interactive.c</file>
    <file>constraints_vfl.c</file>
    <file>constraints_builder.c</file>
    <file>css_accordion.c</file>
    <file>css_basics.c</file>
    <file>css_blendmodes.c</file>
    <file>css_multiplebgs.c</file>
    <file>css_pixbufs.c</file>
    <file>css_shadows.c</file>
    <file>cursors.c</file>
    <file>dialog.c</file>
    <file>drawingarea.c</file>
    <file>dnd.c</file>
    <file>editable_cells.c</file>
    <file>entry_completion.c</file>
    <file>entry_undo.c</file>
    <file>errorstates.c</file>
    <file>expander.c</file>
    <file>filtermodel.c</file>
    <file>fishbowl.c</file>
    <file>fixed.c</file>
    <file>fixed2.c</file>
    <file>flowbox.c</file>
    <file>frames.c</file>
    <file>font_features.c</file>
    <file>fontplane.c</file>
    <file>fontrendering.c</file>
    <file>gears.c</file>
    <file>gestures.c</file>
    <file>glarea.c</file>
    <file>headerbar.c</file>
    <file>hypertext.c</file>
    <file>iconscroll.c</file>
    <file>iconview.c</file>
    <file>iconview_edit.c</file>
    <file>image_filtering.c</file>
    <file>image_scaling.c</file>
    <file>images.c</file>
    <file>infobar.c</file>
    <file>layoutmanager.c</file>
    <file>layoutmanager2.c</file>
    <file>links.c</file>
    <file>listbox.c</file>
    <file>listbox_controls.c</file>
    <file>listview_applauncher.c</file>
    <file>listview_colors.c</file>
    <file>listview_clocks.c</file>
    <file>listview_filebrowser.c</file>
    <file>listview_minesweeper.c</file>
    <file>listview_selections.c</file>
    <file>listview_settings.c</file>
    <file>listview_settings2.c</file>
    <file>listview_ucd.c</file>
    <file>listview_weather.c</file>
    <file>listview_words.c</file>
    <file>list_store.c</file>
    <file>main.c</file>
    <file>markup.c</file>
    <file>mask.c</file>
    <file>overlay.c</file>
    <file>overlay_decorative.c</file>
    <file>paint.c</file>
    <file>pagesetup.c</file>
    <file>paintable.c</file>
    <file>paintable_animated.c</file>
    <file>paintable_emblem.c</file>
    <file>paintable_mediastream.c</file>
    <file>paintable_svg.c</file>
    <file>paintable_symbolic.c</file>
    <file>panes.c</file>
    <file>password_entry.c</file>
    <file>path_explorer_demo.c</file>
    <file>path_fill.c</file>
    <file>path_maze.c</file>
    <file>path_spinner.c</file>
    <file>path_sweep.c</file>
    <file>path_walk.c</file>
    <file>path_text.c</file>
    <file>peg_solitaire.c</file>
    <file>pickers.c</file>
    <file>printing.c</file>
    <file>revealer.c</file>
    <file>read_more.c</file>
    <file>rotated_text.c</file>
    <file>scale.c</file>
    <file>search_entry.c</file>
    <file>shadertoy.c</file>
    <file>shortcuts.c</file>
    <file>shortcut_triggers.c</file>
    <file>sizegroup.c</file>
    <file>sidebar.c</file>
    <file>sliding_puzzle.c</file>
    <file>stack.c</file>
    <file>spinbutton.c</file>
    <file>spinner.c</file>
    <file>tabs.c</file>
    <file>tagged_entry.c</file>
    <file>textundo.c</file>
    <file>textview.c</file>
    <file>textscroll.c</file>
    <file>theming_style_classes.c</file>
    <file>themes.c</file>
    <file>transparent.c</file>
    <file>tree_store.c</file>
    <file>textmask.c</file>
    <file>video_player.c</file>
  </gresource>
  <gresource prefix="/textview">
    <file>floppybuddy.gif</file>
  </gresource>
  <gresource prefix="/tagged_entry">
    <file>tagstyle.css</file>
  </gresource>
  <gresource prefix="/listbox">
    <file>listbox.ui</file>
    <file>messages.txt</file>
    <file>apple-red.png</file>
  </gresource>
  <gresource prefix="/listbox_controls">
    <file>listbox_controls.ui</file>
  </gresource>
  <gresource prefix="/glarea">
    <file>glarea-gl.fs.glsl</file>
    <file>glarea-gl.vs.glsl</file>
    <file>glarea-gles.fs.glsl</file>
    <file>glarea-gles.vs.glsl</file>
  </gresource>
  <gresource prefix="/font_features">
    <file>font_features.ui</file>
    <file>fontplane.c</file>
  </gresource>
  <gresource prefix="/spinbutton">
    <file>spinbutton.ui</file>
  </gresource>
  <gresource prefix="/filtermodel">
    <file>filtermodel.ui</file>
  </gresource>
  <gresource prefix="/overlay2">
    <file>decor1.png</file>
    <file>decor2.png</file>
  </gresource>
  <gresource prefix="/transparent">
    <file>portland-rose.jpg</file>
    <file>bluroverlay.h</file>
    <file>bluroverlay.c</file>
  </gresource>
  <gresource prefix="/markup">
    <file>markup.txt</file>
  </gresource>
  <gresource prefix="/scale">
    <file>scale.ui</file>
  </gresource>
  <gresource prefix="/tagged_entry">
    <file>demotaggedentry.c</file>
    <file>demotaggedentry.h</file>
  </gresource>
  <gresource prefix="/fixed">
    <file>fixed.css</file>
  </gresource>
  <gresource prefix="/fontrendering">
    <file>fontrendering.ui</file>
  </gresource>
  <gresource prefix="/path_explorer_demo">
    <file>path_explorer_demo.ui</file>
    <file>path_explorer_demo.css</file>
  </gresource>
  <gresource prefix="/path_sweep">
    <file>path_sweep.ui</file>
    <file compressed="true">path_world.txt</file>
  </gresource>
  <gresource prefix="/path_walk">
    <file>path_walk.ui</file>
    <file compressed="true">path_world.txt</file>
  </gresource>
  <gresource prefix="/path_text">
    <file>path_text.ui</file>
  </gresource>
  <gresource prefix="/image_filtering">
    <file>portland-rose.jpg</file>
    <file>image_filtering.css</file>
    <file>image_filtering.ui</file>
    <file>filter_paintable.h</file>
    <file>filter_paintable.c</file>
  </gresource>
  <gresource prefix="/org/gtk/Demo4">
    <file>icons/16x16/actions/application-exit.png</file>
    <file>icons/16x16/actions/document-new.png</file>
    <file>icons/16x16/actions/document-open.png</file>
    <file>icons/16x16/actions/document-save.png</file>
    <file>icons/16x16/actions/edit-copy.png</file>
    <file>icons/16x16/actions/edit-cut.png</file>
    <file>icons/16x16/actions/edit-paste.png</file>
    <file>icons/16x16/actions/process-stop.png</file>
    <file>icons/16x16/actions/go-home.png</file>
    <file>icons/16x16/actions/go-up.png</file>
    <file>icons/16x16/emotes/face-cool.png</file>
    <file>icons/16x16/categories/applications-other.png</file>
    <file>icons/48x48/status/starred.png</file>
    <file>icons/scalable/actions/document-edit-symbolic.svg</file>
    <file>icons/scalable/actions/mail-send-receive-symbolic.svg</file>
    <file>icons/scalable/actions/view-fullscreen-symbolic.svg</file>
    <file>icons/scalable/emotes/face-laugh-symbolic.svg</file>
    <file>icons/scalable/status/battery-level-10-charging-symbolic.svg</file>
    <file alias="icons/scalable/apps/org.gtk.Demo4.svg">data/scalable/apps/org.gtk.Demo4.svg</file>
    <file>portland-rose-thumbnail.png</file>
    <file>large-image-thumbnail.png</file>
    <file compressed="true">large-image.png</file>
    <file compressed="true">Moby-Dick.txt</file>
  </gresource>
  <gresource prefix="/org/gtk/Demo4/gtk">
    <file preprocess="xml-stripblanks">help-overlay.ui</file>
  </gresource>
</gresources>
