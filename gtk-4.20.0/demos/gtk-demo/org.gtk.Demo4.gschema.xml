<?xml version="1.0" encoding="UTF-8"?>

<schemalist>

  <enum id='org.gtk.Demo4.Color'>
    <value nick='red'   value='0'/>
    <value nick='green' value='1'/>
    <value nick='blue'  value='2'/>
  </enum>

  <schema id='org.gtk.Demo4' path='/org/gtk/Demo4/'>
    <key name='color' enum='org.gtk.Demo4.Color'>
      <default>'red'</default>
    </key>
    <key name='window-size' type='(ii)'>
      <default>(-1, -1)</default>
    </key>
    <key name='maximized' type='b'>
      <default>false</default>
    </key>
    <key name='fullscreen' type='b'>
      <default>false</default>
    </key>
  </schema>

</schemalist>
