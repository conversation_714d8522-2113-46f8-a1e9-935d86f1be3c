<?xml version="1.0" encoding="UTF-8"?>
<interface>
  <object class="GtkWindow" id="window">
    <property name="default-width">300</property>
    <property name="default-height">300</property>
    <property name="title">Revealer</property>
    <property name="child">
      <object class="GtkGrid">
        <property name="halign">center</property>
        <property name="valign">center</property>
        <child>
          <object class="GtkRevealer" id="revealer0">
            <property name="transition-duration">2000</property>
            <property name="transition-type">crossfade</property>
            <property name="child">
              <object class="GtkImage">
                <property name="icon-name">face-cool-symbolic</property>
                <property name="icon-size">large</property>
              </object>
            </property>
            <layout>
              <property name="column">2</property>
              <property name="row">2</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkRevealer" id="revealer1">
            <property name="transition-duration">2000</property>
            <property name="transition-type">slide-up</property>
            <property name="child">
              <object class="GtkImage">
                <property name="icon-name">face-cool-symbolic</property>
                <property name="icon-size">large</property>
              </object>
            </property>
            <layout>
              <property name="column">2</property>
              <property name="row">1</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkRevealer" id="revealer2">
            <property name="transition-duration">2000</property>
            <property name="transition-type">slide-right</property>
            <property name="child">
              <object class="GtkImage">
                <property name="icon-name">face-cool-symbolic</property>
                <property name="icon-size">large</property>
              </object>
            </property>
            <layout>
              <property name="column">3</property>
              <property name="row">2</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkRevealer" id="revealer3">
            <property name="transition-duration">2000</property>
            <property name="child">
              <object class="GtkImage">
                <property name="icon-name">face-cool-symbolic</property>
                <property name="icon-size">large</property>
              </object>
            </property>
            <layout>
              <property name="column">2</property>
              <property name="row">3</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkRevealer" id="revealer4">
            <property name="transition-duration">2000</property>
            <property name="transition-type">slide-left</property>
            <property name="child">
              <object class="GtkImage">
                <property name="icon-name">face-cool-symbolic</property>
                <property name="icon-size">large</property>
              </object>
            </property>
            <layout>
              <property name="column">1</property>
              <property name="row">2</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkRevealer" id="revealer5">
            <property name="transition-duration">2000</property>
            <property name="transition-type">slide-up</property>
            <property name="child">
              <object class="GtkImage">
                <property name="icon-name">face-cool-symbolic</property>
                <property name="icon-size">large</property>
              </object>
            </property>
            <layout>
              <property name="column">2</property>
              <property name="row">0</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkRevealer" id="revealer6">
            <property name="transition-duration">2000</property>
            <property name="transition-type">slide-right</property>
            <property name="child">
              <object class="GtkImage">
                <property name="icon-name">face-cool-symbolic</property>
                <property name="icon-size">large</property>
              </object>
            </property>
            <layout>
              <property name="column">4</property>
              <property name="row">2</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkRevealer" id="revealer7">
            <property name="transition-duration">2000</property>
            <property name="child">
              <object class="GtkImage">
                <property name="icon-name">face-cool-symbolic</property>
                <property name="icon-size">large</property>
              </object>
            </property>
            <layout>
              <property name="column">2</property>
              <property name="row">4</property>
            </layout>
          </object>
        </child>
        <child>
          <object class="GtkRevealer" id="revealer8">
            <property name="transition-duration">2000</property>
            <property name="transition-type">slide-left</property>
            <property name="child">
              <object class="GtkImage">
                <property name="icon-name">face-cool-symbolic</property>
                <property name="icon-size">large</property>
              </object>
            </property>
            <layout>
              <property name="column">0</property>
              <property name="row">2</property>
            </layout>
          </object>
        </child>
      </object>
    </property>
  </object>
</interface>
