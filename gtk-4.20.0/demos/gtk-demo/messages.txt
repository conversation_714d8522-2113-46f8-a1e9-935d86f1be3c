1|GTK+ and friends|GTKtoolkit|@breizhodrome yeah, that's for the OpenGL support that has been added recently|1416751697|0||2|1
2|<PERSON><PERSON>|ebassi|RT @ebassi: embloggeration happened: http://t.co/9ukkNuSzuc — help out supporting GL on windows and macos in GTK+ 3.16.|1416086824|0|GTKtoolkit|0|9
3|<PERSON>|ystreet00|RT @ystreet00: .@GTKtoolkit + @gstreamer integration using the new #gtk #opengl support https://t.co/IeBpFjbjes http://t.co/WptPHCfFIb|1416086780|0|GTKtoolkit|0|13
4|<PERSON><PERSON>|ebas<PERSON>|RT @ebassi: embloggeration happened — OpenGL integration lands in GTK+ — http://t.co/sUGwcvZhRg|1413214719|0|GTKtoolkit|0|8
5|Allan <PERSON>|allanday|RT @allanday: New Human Interface Guidelines coming for @gnome and @GTKtoolkit . http://t.co/SMNndyo6rl|1408615736|0|GTKtoolkit|0|12
6|<PERSON>|hergertme|RT @hergertme: being able to set opacity on an individual widget in gtk ... you've come a long way since 2.x days.|1408601183|0|GTKtoolkit|0|2
7|Richard Brown|sysrich|RT @sysrich: hmm, good thing Iike eating with chopsticks #GUADEC http://t.co/7aG9CYpdZg|1406543731|0|GTKtoolkit|0|82
8|Javier Jardón|jjardon|RT @jjardon: #GNOME 3.13.4 has just been released from Strasbourg, this year #GUADEC city. Enjoy! https://t.co/hgHDVOWvRC|1406303072|0|GTKtoolkit|0|6
9|GNOME|gnome|RT @gnome: This year's @guadec schedule has been published. Lots of great talks on there, as usual. https://t.co/rpGPxIRCuB|1405929795|0|GTKtoolkit|0|20
10|GTK+ and friends|GTKtoolkit|New features of GtkInspector : http://t.co/EOgcv1lh8D #gtk #gtk3|1402076874|0||2|3
11|The Valeyard|breizhodrome|RT @breizhodrome: @GTKtoolkit and his multipoint gesture, good thing for mobile applications :) #Gtk|1402076810|0|GTKtoolkit|0|1
12|GTK+ and friends|GTKtoolkit|@Gin_Cheng sorry about that, should be fixed now|1402076785|0||0|0
13|GTK+ and friends|GTKtoolkit|@teadriven sorry about that, should be fixed now|1402076751|0||0|0
14|Javier Jardón|jjardon|RT @jjardon: #GNOME 3.13.2 (developer version) released! Try it, brake it, file bugs! https://t.co/cyCLBCtqKo|1401318926|0|GTKtoolkit|0|2
15|GTK+ and friends|GTKtoolkit|Gtkparasite has been integrated in #GTK+: Introducing gtkinspector: http://t.co/dP3DzgPNM3 #gtk3|1400231807|0||8|11
16|GTK+ and friends|GTKtoolkit|GTK+ 3.12 released! Improvements in Wayland, Broadway, OSX ... New widgets: GtkFlowBox,GtkActionBar and GtkPopover: https://t.co/5hBIlfrxc3|1395842503|0||5|8
17|Javier Jardón|jjardon|RT @jjardon: Second beta of #GNOME 3.12 just released! https://t.co/8oTfZaatVr|1394147916|0|GTKtoolkit|0|3
18|Javier Jardón|jjardon|RT @jjardon: First beta of GNOME 3.12 (3.11.90) has just been released. Enjoy! https://t.co/d5wzYWXUnv #gnome|1393006697|0|GTKtoolkit|0|4
19|GTK+ and friends|GTKtoolkit|Some thoughts on portability by @desrt : http://t.co/zyFT6i4we3 #glib|1392903834|0||1|0
20|GTK+ and friends|GTKtoolkit|Popovers support merged in master: http://t.co/5JE0RLhEDo Thanks @garnacho for getting this done! #gtk3|1390500627|0||5|7
21|GTK+ and friends|GTKtoolkit|The continuous build environment now generates 64-bit #GTK+ Windows bundles! Read the announcement from @tarnyko : https://t.co/wXVOAzCYTt|1386169565|0||6|10
22|GTK+ and friends|GTKtoolkit|GTK+ 3 packages for Windows available! Thanks for the hard work of @tarnyko to make this possible!\nhttp://t.co/U9JgsGoBLm|1382633636|0||7|23
23|GTK+ and friends|GTKtoolkit|Status of support of high resolution displays in #GTK+ (and #GNOME ) http://t.co/SPQN2E6Qxo Thanks to Brion Vibber for the donation!|1372531560|0||2|3
24|Javier Jardón|jjardon|RT @jjardon: Firefox GTK+3 port ready for testing https://t.co/onpxJaTKO5 #gtk #gtk3|1371557291|0|GTKtoolkit|0|22
25|GTK+ and friends|GTKtoolkit|GTK+ 3.8.0 (STABLE) released: wayland, Multi-application Broadway, improved CSS support and more ... http://t.co/RlLmrNPyYs #gtk #gtk3|1364435230|0||0|5
26|Daniel Svensson|dsvensson|RT @dsvensson: Bringing an application up to the new features in GTK 3.x = tons of negative diffs, awesome work by @GTKtoolkit devs <3|1352906611|0|GTKtoolkit|0|3
27|GTK+ and friends|GTKtoolkit|GLib status update and a warning: http://t.co/quQP8dLf #glib|1352905826|0||1|1
28|GTK+ and friends|GTKtoolkit|GProperty status: http://t.co/Nk28V2Rh #glib|1352905797|0||1|1
29|GTK+ and friends|GTKtoolkit|GTK+ 3.6.2 (STABLE) available: http://t.co/ah87o7cC #gtk #gtk3|1352905768|0||1|2
30|GTK+ and friends|GTKtoolkit|GLib 2.34.2 (STABLE) available: http://t.co/yavkTJwr #glib|1352905722|0||2|1
31|GTK+ and friends|GTKtoolkit|GTK+ 3.6.0 (STABLE) released: http://t.co/3NDAT5K9 #gtk #gtk3|1350075620|0||0|4
32|GTK+ and friends|GTKtoolkit|GLib 2.34.0 (STABLE) released: http://t.co/eWRD7hNy #glib|1350075583|0||0|6
33|GTK+ and friends|GTKtoolkit|GLib 2.33.10 (UNSTABLE) released: http://t.co/3BCdOPDy #glib|1347299317|0||2|2
34|Javier Jardón|jjardon|RT @jjardon: GnomeGoals status update: https://t.co/q5j7mJ1c #gnome|1342143404|0|GTKtoolkit|0|1
35|Emmanuele Bassi|ebassi|RT @ebassi: Saturday, 28/07, 11:45 - I'll be talking about Rainbows and Unicorns @ GUADEC https://t.co/WOiF6QU6|1341984820|0|GTKtoolkit|0|2
36|Harvey|cd0|RT @cd0: According to the sourcecode zipball the browser in the samsung smart tvs (UNxxES8xxx) is webkit-gtk 20120109. Not bad. @GTKtoolkit|1341712733|0|GTKtoolkit|0|3
37|Claudio Saavedra|csaavedra|RT @csaavedra: Accelerated compositing in WebKitGTK+: http://t.co/yxl0BooF #webkit #gnome|1341712291|0|GTKtoolkit|0|2
38|GTK+ and friends|GTKtoolkit|GTK+ 3.5.6 (UNSTABLE) released, now featuring GtkSearchEntry and GtkMenuButton http://t.co/adHtm2OA #gtk #gtk3|1341689740|0||0|3
39|GTK+ and friends|GTKtoolkit|GTK+ 3.4.0 (STABLE) released: http://t.co/KPSfJQSg #gtk #gtk3|1332870781|0||0|17
40|Javier Jardón|jjardon|RT @jjardon: GTK+ 2.24.9 (STABLE) released: https://t.co/OgcL5jnR #gtk|1327437929|0|GTKtoolkit|0|2
41|GTK+ and friends|GTKtoolkit|Multitouch is near… by @garnacho http://t.co/68iK8m9S #gtk #gtk3|1327090575|0||1|7
42|GTK+ and friends|GTKtoolkit|@dylanmccall Follow this bug: https://t.co/9vCpBVSm|1326802580|0||0|0
43|GTK+ and friends|GTKtoolkit|@cd0 Nice. Please, report any issue next time ;)|1326802460|0||0|0
44|GTK+ and friends|GTKtoolkit|RFC: new features http://t.co/uiqYWx4O #gtk #gtk3|1326802266|0||1|2
45|GTK+ and friends|GTKtoolkit|@cd0 Did you file a bug?|1326776652|0||0|0
46|GTK+ and friends|GTKtoolkit|@dylanmccall You mean this? http://t.co/BXbocqE9|1326776459|0||0|0
47|GTK+ and friends|GTKtoolkit|@trufae https://t.co/xlq75hDL|1326776153|0||0|0
48|GTK+ and friends|GTKtoolkit|RFC: UI design: http://t.co/Lu8Gnnfg #gtk #gtk3|1326305191|0||2|2
49|GTK+ and friends|GTKtoolkit|#win32 users: GTK+ 2.24.8 bundles available here: http://t.co/WhuY2XoN It not needed to use 2.16 anymore #gtk|1323190462|0||1|4
50|GTK+ and friends|GTKtoolkit|RFC: Model-View-Controller http://t.co/Lmw4lW9V #gtk #gtk3|1321546108|0||1|1
51|GTK+ and friends|GTKtoolkit|RFC:boxes http://t.co/eZABFgTp #gtk #gtk3|1321546061|0||2|1
52|GTK+ and friends|GTKtoolkit|GTK+ 2.24.8 (stable) released: update of the win32 backend, it now works at least as well as the old 2.16.x http://t.co/6wrhs7hm #gtk|1321297367|0||0|2
53|GTK+ and friends|GTKtoolkit|GTK + #Clutter next step(s): http://t.co/UDIezbyW #gtk #gtk4|1318265984|0||3|4
54|GTK+ and friends|GTKtoolkit|Tutorial for #Python, #GStreamer and #GTK 3: http://t.co/hvfRx18E #gtk3|1317781925|0||5|0
55|GTK+ and friends|GTKtoolkit|@jonobacon nice, but pyGTK is deprecated, use pygobject instead|1317353873|0||1|0
56|GTK+ and friends|GTKtoolkit|GTK+ 3.2 (STABLE) released: http://t.co/EqHjTmol #gtk #gtk3|1317043650|0||0|11
57|GTK+ and friends|GTKtoolkit|New D-Bus features in GLib 2.30: http://t.co/rzHui2Q2 #gtk #glib|1316732697|0||3|4
58|Lanedo GmbH|LanedoTweets|RT @TimJanik: New #GTK+ building instructions for #Mac OS X now up in the #GNOME wiki: http://t.co/lLt2fb1B|1316646621|0|GTKtoolkit|0|3
59|GTK+ and friends|GTKtoolkit|GTK+ 3.1.90 (UNSTABLE) released: http://t.co/KRz34jp #gtk #gtk3|1315961535|0||0|3
60|Lanedo GmbH|LanedoTweets|RT @TimJanik: There's a Win32 security advisory for Gtk+, it's recommended to upgrade to latest Gtk+ (2.24.6) if you haven't yet: http:/ ...|1315914861|0|GTKtoolkit|0|5
61|GTK+ and friends|GTKtoolkit|GTK+ 4.0 and #Clutter 2.0: rainbows and unicorns: http://t.co/SKbl0vQ #gtk #gtk4|1314883483|0||2|14
62|GTK+ and friends|GTKtoolkit|Some #Glib plans for the next cycle: http://t.co/a6YybK0 #gtk|1314883427|0||0|3
63|Nat Friedman|natfriedman|RT @natfriedman: Any Gtk+ experts who want to make some consulting money fixing bugs in Gtk/Mac, email me: <EMAIL>.|1314355269|0|GTKtoolkit|0|28
64|Kristian Rietveld|krietvel|RT @krietvel: Blog post: 'Merged “treemodel-fix” branch into GTK+: call for testing, blog post series' http://t.co/yAUnneo #gtk|1314096198|0|GTKtoolkit|0|2
65|GTK+ and friends|GTKtoolkit|@ArcherSeven Help improving the patch here: http://t.co/r74hP79|1313493595|0||0|0
66|GTK+ and friends|GTKtoolkit|GTK+ 3.1.12 (UNSTABLE) released: http://t.co/3iPAlNq Try the new Font Dialog! #gtk #gtk3|1313493256|0||0|4
67|GTK+ and friends|GTKtoolkit|@cimi @DanielFore Patches always welcomed!|1313493010|0||0|0
68|GTK+ and friends|GTKtoolkit|a11y branch was merged into master: http://mail.gnome.org/archives/gtk-devel-list/2011-July/msg00004.html #gtk #gtk3|1309962425|0||0|2
69|GTK+ and friends|GTKtoolkit|Another update in the effort to improve #a11y in #gtk: http://mail.gnome.org/archives/gtk-devel-list/2011-June/msg00057.html #gtk3|1309606597|0||0|0
70|GTK+ and friends|GTKtoolkit|@cd0 What is wrong in that page? freetype already appears as a required dependency. Anyway patches always welcomed ;)|1307359139|0||0|0
71|GTK+ and friends|GTKtoolkit|Of course, everyone is welcomed to improve the #gtk website. git repo: http://ur1.ca/4bwbw bugzilla: http://ur1.ca/4bwc1|1307038767|0||0|1
72|GTK+ and friends|GTKtoolkit|Check out the new #gtk website!!: www.gtk.org|1307036644|0||0|4
73|GTK+ and friends|GTKtoolkit|@jikri Take a look to http://live.gnome.org/action/login/GTK+/Roadmap and http://developer.gnome.org/gtk3/stable/gtk-migrating-2-to-3.html|1306673774|0||0|0
74|GTK+ and friends|GTKtoolkit|Introducing Cossa, a GTK+ theme previewer for gedit, by @garnacho http://ur1.ca/4ate8 #gtk #gtk3|1306672611|0||1|5
75|GTK+ and friends|GTKtoolkit|#GProperty, new API for Property and Accessor declaration, by @ebassi : http://ur1.ca/47lgk #gtk #glib|1305717028|0||0|2
76|GTK+ and friends|GTKtoolkit|GLib 2.29.4 (UNSTABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-May/msg00012.html #gtk #glib|1304593138|0||0|2
77|GTK+ and friends|GTKtoolkit|RT @acruiz: Gtk+ FontSelection progress http://bit.ly/iikP2f #gtk #gtk3|1303089979|0||0|1
78|GTK+ and friends|GTKtoolkit|RT @krietvel: New blog post: CoreText backend now in Pango master http://bit.ly/dTE0a1 #gtk #pango #osx|1303089938|0||0|0
79|GTK+ and friends|GTKtoolkit|GTK+ 3.0.9 (STABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-April/msg00087.html #gtk #gtk3|1302883958|0||0|0
80|GTK+ and friends|GTKtoolkit|GLib 2.28.6 (STABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-April/msg00074.html #gtk #glib|1302780112|0||0|3
81|GTK+ and friends|GTKtoolkit|GTK+ 3.1.2 (UNSTABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-April/msg00072.html #gtk #gtk3|1302737279|0||0|2
82|GTK+ and friends|GTKtoolkit|GLib 2.29.2 (UNSTABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-April/msg00071.html #gtk #glib|1302702936|0||0|3
83|Kristian Høgsberg|hoegsberg|RT @hoegsberg: yay, merged the Wayland GTK+ backend to the master branch - no, it's still not compete.|1302621000|0|GTKtoolkit|0|9
84|GTK+ and friends|GTKtoolkit|Someone willing to help with the client side decorations branch? http://git.gnome.org/browse/gtk+/log/?h=client-side-decorations #gtk #gtk3|1302620918|0||1|1
85|GTK+ and friends|GTKtoolkit|#Wayland GTK+ backend merged in master: http://git.gnome.org/browse/gtk+/commit/?id=c7514e8f0d19a833257497caff413bb4dfae6eb4 #gtk #gtk3|1302620838|0||1|9
86|GTK+ and friends|GTKtoolkit|gtkmm 3.0.0 (STABLE) released: http://mail.gnome.org/archives/gtkmm-list/2011-April/msg00025.html #gtk #cpp|1302355894|0||0|3
87|GTK+ and friends|GTKtoolkit|RT @alex_igalia: WebKit2 MiniBrowser for the GTK+ port running! http://ur1.ca/3t3ov #gtk #webkit|1302261488|0||1|0
88|GTK+ and friends|GTKtoolkit|#GNOME3 is out, using all the power of #gtk3 , congrats everyone! #gtk #gnome|1302219444|0||0|2
89|GTK+ and friends|GTKtoolkit|HTML5 backend update, now with real toplevel windows!! http://blogs.gnome.org/alexl/2011/04/07/broadway-update-2/ #gtk #gtk3|1302218981|0||2|10
90|GTK+ and friends|GTKtoolkit|Glade 3.10 (STABLE) released: With support for GTK+3, pygobject and all the new stuff: http://ur1.ca/3s8wk #rad #gtk|1302032523|0||0|6
91|GTK+ and friends|GTKtoolkit|GTK+ 3.0.8 (STABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-April/msg00009.html #gtk #gtk3|1301878240|0||0|4
92|GTK+ and friends|GTKtoolkit|GTK+ latest performance improvements (with video): http://blogs.gnome.org/otte/2011/03/30/the-book-was-better/ #gtk #gtk3|1301594293|0||1|6
93|GTK+ and friends|GTKtoolkit|Benjamin Otte is improving GTK+ performance with some impressive results, check latest commits #gtk #gtk3|1301409776|0||1|2
94|Javier Jardón|jjardon|RT @jjardon: Also nice to see that a firefox GTK+3 port was started: https://bugzilla.mozilla.org/show_bug.cgi?id=627699 #gtk #gtk3 #fi ...|1301166992|0|GTKtoolkit|0|8
95|GTK+ and friends|GTKtoolkit|RT @krietvel: Oh yea, I still have to upstream the CoreText backend I wrote for Pango. Completely forgot about that. #gtk|1301149034|0||0|0
96|GTK+ and friends|GTKtoolkit|GTK+ 3.0.5 (STABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-March/msg00099.html #gtk #gtk3|1300925808|0||0|1
97|GTK+ and friends|GTKtoolkit|Nice article of @cgwalters about analyzing memory use in #glib with #SystemTap: http://ur1.ca/3m0ak #gtk|1300672197|0||2|1
98|GTK+ and friends|GTKtoolkit|GLib 2.28.3 (STABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-March/msg00065.html #gtk #glib|1300374677|0||0|2
99|GTK+ and friends|GTKtoolkit|GTK+ HTML backend merged: http://blogs.gnome.org/alexl/2011/03/15/gtk-html-backend-update/ #gtk #gtk3|1300334447|0||3|4
100|GTK+ and friends|GTKtoolkit|GTK+ 3.0.3 (STABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-March/msg00059.html #gtk #gtk3|1300151113|0||0|5
101|GTK+ and friends|GTKtoolkit|PyGObject (new gobject introspection-based bindings) 2.28.0 (STABLE) released: http://ur1.ca/3fcsp #python #gtk|1299620983|0||0|1
102|GTK+ and friends|GTKtoolkit|GTK+ team meeting now in #gtk-devel on irc.gimp.net #gtk|1299615099|0||0|1
103|GTK+ and friends|GTKtoolkit|REMINDER: GTK+ Team IRC Meeting - 2011-03-08 at 20:00 UTC: http://ur1.ca/3ezpn Agenda: http://ur1.ca/3ezpp #gtk|1299517986|0||0|0
104|GTK+ and friends|GTKtoolkit|GTK+ 3.0.2 (STABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-March/msg00010.html #gtk|1299517909|0||1|3
105|GTK+ and friends|GTKtoolkit|PyGObject, the new Python introspection based bindings almost ready for the 2.28 stable release: http://ur1.ca/3dfaj #python #gtk|1299081554|0||0|5
106|GTK+ and friends|GTKtoolkit|GTK+ 3.0.1 (STABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2011-February/msg00088.html #gtk|1298379744|0||0|4
107|GTK+ and friends|GTKtoolkit|More features/ideas for gtk+ 3.2: pictures: https://mail.gnome.org/archives/gtk-devel-list/2011-February/msg00038.html #gtk|1297815657|0||1|2
108|GTK+ and friends|GTKtoolkit|New features/ideas for gtk+ 3.2: Translucent TextViews : http://blogs.gnome.org/tvb/2011/02/14/translucent-textviews/ #gtk|1297707521|0||1|2
109|GTK+ and friends|GTKtoolkit|Blog post of our tireless maintainer, Matthias Clasen: http://blogs.fedoraproject.org/wp/mclasen/2011/02/10/gtk-3-is-here/ #gtk #gtk3|1297378021|0||0|5
110|GTK+ and friends|GTKtoolkit|Highlights: Cairo-based, XI2, new theming API, Flexible geometry management, Multiple backend support for GDK, easy application support ...|1297373117|0||0|3
111|GTK+ and friends|GTKtoolkit|GTK+ 3.0 released!! : http://mail.gnome.org/archives/gtk-devel-list/2011-February/msg00020.html gtk!|1297372977|0||1|20
112|GTK+ and friends|GTKtoolkit|GLib 2.28.0 (stable) released: https://mail.gnome.org/archives/gtk-devel-list/2011-February/msg00014.html #gtk #glib|1297196093|0||0|2
113|GTK+ and friends|GTKtoolkit|GTK+ 2.99.3 released: latest beta before GTK+3 http://mail.gnome.org/archives/gtk-devel-list/2011-February/msg00004.html #gtk|1296609072|0||1|1
114|GTK+ and friends|GTKtoolkit|Glade 3.9.2 released: off screen, workspace new look, GtkComboBoxText, GtkFileFilter, GtkApplication and more! http://ur1.ca/335is #gtk #rad|1296608221|0||1|0
115|GTK+ and friends|GTKtoolkit|GTK+ 2.24 ( STABLE ) released: This will be the latest 2.x release. http://ur1.ca/32cft #gtk|1296438857|0||2|0
116|GTK+ and friends|GTKtoolkit|More progress on #Glade, the GTK+ #interface #designer: http://ur1.ca/2uzpa Note that Glade 3.8 -> #gtk2 and Glade 3.10-> #gtk3 #gtk|1295186227|0||0|2
117|GTK+ and friends|GTKtoolkit|RT @prcutler RT @fcrozat: First shot at GNOME3 evaluation usb stick : http://bit.ly/i1wM8X #gnome #gnome3 #gnome-shell #opensuse #gnome|1295186033|0||1|0
118|GTK+ and friends|GTKtoolkit|If you want to try the #wayland backend, checkout this branch: http://git.gnome.org/browse/gtk+/log/?h=gdk-backend-wayland #gtk|1294739562|0||1|5
119|GTK+ and friends|GTKtoolkit|GTK+ 2.99.1 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2011-January/msg00005.html #gtk|1294738413|0||0|0
120|GTK+ and friends|GTKtoolkit|GTK+ 2.99: It is now possible to include multiple GDK backends in a single library. Use --enable-{x11,win32,quartz}-backend #gtk|1294344201|0||0|0
121|GTK+ and friends|GTKtoolkit|GTK+ 2.99: The removal of GSEALEd struct members has been completed in this release #gtk|1294344070|0||0|1
122|GTK+ and friends|GTKtoolkit|GTK+ 2.99.0 (unstable) released http://mail.gnome.org/archives/gtk-devel-list/2011-January/msg00001.html #gtk|1294344044|0||0|2
123|GTK+ and friends|GTKtoolkit|GLib 2.27.90 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2011-January/msg00000.html #glib|1294343933|0||0|0
124|GTK+ and friends|GTKtoolkit|Glade 3.9.0 (unstalbe) released: snapshot leading up to Glade 3.10 that will depend on GTK+3 http://ur1.ca/2rir0 #gtk #rad|1294343894|0||0|1
125|GTK+ and friends|GTKtoolkit|Glade 3.7.3 (unstable) released: snapshot leading up to Glade 3.8 that\nwill depend on GTK+ 2.24 http://ur1.ca/2riqg #gtk|1294343835|0||0|0
126|GTK+ and friends|GTKtoolkit|RT @hoegsberg: Multi-backend support in GTK+: http://bit.ly/gDwugJ - switch between #Wayland and X11 by setting GDK_BACKEND #gtk|1294201849|0||0|0
127|GTK+ and friends|GTKtoolkit|RT @krietvel Blog post \"GDK 3.0 on Mac OS X\" http://bit.ly/ihr9kH or how GDK became awesome in GTK+ 3.0. #gtk #osx|1293728637|0||0|1
128|GTK+ and friends|GTKtoolkit|RT @krietvel Blog post \"Refactoring GtkTreeView using GtkCellArea\" http://bit.ly/g9aArE #gtk|1293728607|0||0|0
129|GTK+ and friends|GTKtoolkit|Also, the treeview-refactor branch has been merged too|1293036166|0||1|0
130|GTK+ and friends|GTKtoolkit|New in GTK+ 2.91.7: gdk-backend branch have been merged: the goal is allowing to build a single gdk library that contains multiple backends|1293036118|0||0|0
131|GTK+ and friends|GTKtoolkit|GTK+ 2.91.7 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-December/msg00155.html #gtk|1293035980|0||1|2
132|GTK+ and friends|GTKtoolkit|GTK+ 2.23.3 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-December/msg00156.html #gtk|1293035865|0||0|2
133|GTK+ and friends|GTKtoolkit|GLib 2.27.5 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-December/msg00152.html #gtk #glib|1293035786|0||0|0
134|GTK+ and friends|GTKtoolkit|Glade 3.7.2 (unstable) released: http://lists.ximian.com/pipermail/glade-devel/2010-December/001853.html #gtk #RAD|1292589571|0||0|0
135|Andrea Cimitan|cimi|RT @cimi: reading migration docs, later Murrine will start to be ported over GtkStyleContext (so CSS fun :))|1291813590|0|GTKtoolkit|0|1
136|GTK+ and friends|GTKtoolkit|Work to building multiple backends on the same system started: http://ur1.ca/2ieid #gtk|1291614285|0||0|2
137|GTK+ and friends|GTKtoolkit|RT @garnacho : gtk-style-context landed in GTK+ master, if gnome3 looks temporarily uglier that was me :) #gtk #gnome|1291613819|0||0|0
138|Stormy|storming|RT @storming: Anyone know of any call centers that use GNOME? Potential funding for a11y work if we do ...|1291387291|0|GTKtoolkit|0|4
139|GTK+ and friends|GTKtoolkit|Final part of the Benjamin Otte GTK3 rendering\ncleanup has landed: http://ur1.ca/2hrc9 #gtk|1291375493|0||0|0
140|GTK+ and friends|GTKtoolkit|ANNOUNCE: gtkmm 2.91.5 (unstable) released: http://mail.gnome.org/archives/gtkmm-list/2010-December/msg00000.html #gtk #bindings #cplusplus|1291212942|0||0|0
141|GTK+ and friends|GTKtoolkit|ANNOUNCE: glibmm 2.27.4 (unstable) released: http://ur1.ca/2h77z #glib #bindings #cplusplus|1291212883|0||0|0
142|GTK+ and friends|GTKtoolkit|GtkAppChooser landed in master: https://bugzilla.gnome.org/show_bug.cgi?id=582557#c10 #gtk|1291212784|0||0|0
143|GTK+ and friends|GTKtoolkit|larger changes in GTK+ soon: GtkStyleContext, rendering-cleanup, app-chooser branch, GtkRadioGroup branch, http://ur1.ca/2gs5u #gtk|1291211812|0||2|0
144|GTK+ and friends|GTKtoolkit|New widget: GtkSwitch http://blogs.fedoraproject.org/wp/mclasen/2010/11/29/onoff/ thanks to @ebassi and Matthias Clasen for the review #gtk|1291211711|0||2|1
145|GTK+ and friends|GTKtoolkit|GTK+ html backend (broadway branch) landed: http://mail.gnome.org/archives/gtk-devel-list/2010-November/msg00103.html #gtk|1291211452|0||0|1
146|GTK+ and friends|GTKtoolkit|GTK+ 2.91.5 (unstalbe) released: http://mail.gnome.org/archives/gtk-devel-list/2010-November/msg00109.html #gtk|1291211337|0||0|1
147|GTK+ and friends|GTKtoolkit|GLib 2.27.4 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-November/msg00108.html #gtk #glib|1291211331|0||0|0
148|GTK+ and friends|GTKtoolkit|ANNOUNCE: gtkmm 2.91.4 released: http://mail.gnome.org/archives/gtkmm-list/2010-November/msg00095.html #gtk #cplusplus #bindings|1290451737|0||0|0
149|GTK+ and friends|GTKtoolkit|Introducing GtkCellArea: height-for-width geometry management for GtkTreeViews http://ur1.ca/2e5pe #gtk|1290310899|0||1|2
150|Javier Jardón|jjardon|RT @jjardon: gtk+3 packages landed in #Debian experimental: http://packages.debian.org/experimental/libgtk3.0-0 #gtk|1290036413|0|GTKtoolkit|0|2
151|GTK+ and friends|GTKtoolkit|WIP Porting guide to migrate from GTK+2 to GTK+3: http://ur1.ca/1xbzs #gtk #xfce #lxde #gnome|1290036396|0||4|6
152|GTK+ and friends|GTKtoolkit|#GSettings is fast (really): http://blogs.gnome.org/desrt/2010/11/15/gsettings-is-fast/ #gtk #glib|1289853926|0||1|2
153|GTK+ and friends|GTKtoolkit|Help making Glade ready for GTK+ 3: http://blogs.gnome.org/johannes/2010/11/15/help-making-glade-ready-for-3-0/ #gtk|1289853600|0||1|0
154|GTK+ and friends|GTKtoolkit|Anyone up to fix it? RT @vwduder: I wish #gtk wouldn't actually show the window until all of the contents have been rendered to the drawable|1289434823|0||0|0
155|GTK+ and friends|GTKtoolkit|PyGObject 2.27.0 (unstable) released: http://mail.gnome.org/archives/python-hackers-list/2010-November/msg00013.html #python #bindings #gtk|1289431671|0||0|0
156|GTK+ and friends|GTKtoolkit|glibmm 2.27.3 (unstable) released: http://mail.gnome.org/archives/gtkmm-list/2010-November/msg00058.html #glib #cplusplus #bindings|1289387769|0||0|0
157|GTK+ and friends|GTKtoolkit|GLib 2.27.3 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-November/msg00043.html #gtk #glib|1289322725|0||0|1
158|GTK+ and friends|GTKtoolkit|GTK+ 2.91.3 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-November/msg00010.html #gtk|1288758787|0||0|1
159|GTK+ and friends|GTKtoolkit|GLib 2.27.2 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-November/msg00002.html #gtk #glib|1288758720|0||0|0
160|GTK+ and friends|GTKtoolkit|Gtk 3.0 motto: \"We are fixing it!\"|1288630204|0||1|4
161|GTK+ and friends|GTKtoolkit|Recent Openismus contributions to @GtkToolkit http://bit.ly/amuAdX Thank you guys!|1288343314|0||0|0
162|GTK+ and friends|GTKtoolkit|ANNOUNCE: gtkmm 2.91.2 (unstable) released: http://mail.gnome.org/archives/gtkmm-list/2010-October/msg00058.html #gtk #cplusplus|1288098381|0||0|0
163|GTK+ and friends|GTKtoolkit|ANNOUNCE: glibmm (unstable) 2.27.1 released: http://mail.gnome.org/archives/gtkmm-list/2010-October/msg00059.html #gtk #cplusplus|1288098335|0||0|0
164|GTK+ and friends|GTKtoolkit|GTK+ 2.91.2 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-October/msg00230.html #gtk|1288058960|0||0|0
165|GTK+ and friends|GTKtoolkit|GLib 2.27.1 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-October/msg00222.html #gtk #glib|1288049934|0||0|1
166|GTK+ and friends|GTKtoolkit|@garnacho shows off 3.0 CSS awesomness as a result of his work at #gtkhackfest http://bit.ly/aV99F|1288015424|0||0|0
167|GTK+ and friends|GTKtoolkit|GtkGrid landed:new container similar to GtkTable without unnecessary restrictions.It does height-for-width geometry management. #gtkhackfest|1287759681|0||0|1
168|GTK+ and friends|GTKtoolkit|RT @bertogg: The GTK+ artillery: http://flic.kr/p/8M16nu http://flic.kr/p/8M16d7 #gtkhackfest #gtk|1287744902|0||0|0
169|GTK+ and friends|GTKtoolkit|GtkScrollable interface landed in master: http://git.gnome.org/browse/gtk+/commit/?id=55196a705f00564a44647bfc97981db0a783369a #gtk|1287744793|0||0|0
170|Kristian Rietveld|krietvel|RT @krietvel: Blogged on \"Optimizing legacy code\". Or \"Color space conversion is more expensive than you might think\". http://bit.ly/duA ...|1287711819|0|GTKtoolkit|0|1
171|GTK+ and friends|GTKtoolkit|Rounded corners in GtkEntry (thanks Boram Park!) http://ur1.ca/257f0 #gtk #gtkhackfest|1287711709|0||0|6
172|Berto Garcia|bertogg|RT @bertogg: Ryan and Benjamin discussing GtkStyle at the Hercules Tower #gtkhackfest http://twitgoo.com/1pw774|1287708209|0|GTKtoolkit|0|1
173|GTK+ and friends|GTKtoolkit|RT @bertogg Working late at night #gtkhackfest http://twitgoo.com/1pvw46 #gtk|1287602614|0||0|0
174|GTK+ and friends|GTKtoolkit|WIP docs of the new theme API : http://mail.gnome.org/archives/gtk-devel-list/2010-October/msg00134.html #gtkhackfest #gtk|1287564745|0||0|0
175|GTK+ and friends|GTKtoolkit|GtkApplication landed in master: http://ur1.ca/24fhe Feedback welcome #gtk #gtkhackfest|1287564609|0||0|0
176|Emmanuele Bassi|ebassi|RT @ebassi: lots of discussions at the #gtkhackfest - it's great to see the roadmap for 4.0 take shape|1287564383|0|GTKtoolkit|0|2
177|Berto Garcia|bertogg|RT @bertogg: Update from the GTK+ Hackfest 2010: http://blogs.igalia.com/berto/2010/10/19/gtk-hackfest-2010/ #gtkhackfest|1287564380|0|GTKtoolkit|0|3
178|Berto Garcia|bertogg|RT @bertogg: Photos from the #gtkhackfest in Coruña: http://www.flickr.com/photos/tags/gtkhackfest2010/ #igalia #gnome|1287564365|0|GTKtoolkit|0|2
179|GTK+ and friends|GTKtoolkit|ANNOUNCE: gtkmm 2.91.1 released: http://mail.gnome.org/archives/gtkmm-list/2010-October/msg00033.html #gtk #cplusplus|1287478956|0||1|0
180|GTK+ and friends|GTKtoolkit|The #gtkhackfest started today. Thanks a lot to the event sponsors: #igalia, #lanedo, #codethink and the #GNOME foundation.|1287447826|0||0|2
181|GTK+ and friends|GTKtoolkit|#gtkhackfest started today at the #igalia offices in A Coruña http://live.gnome.org/Hackfests/GTK2010 #gnome #gtk|1287422775|0||0|0
182|GTK+ and friends|GTKtoolkit|libnotify, gtk API changes in 2.91.1: http://mail.gnome.org/archives/desktop-devel-list/2010-October/msg00193.html #gtk #gnome #xfce #lxde|1287324866|0||0|0
183|GTK+ and friends|GTKtoolkit|GTK+ 2.91.1 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-October/msg00127.html #gtk|1287324761|0||0|1
184|GTK+ and friends|GTKtoolkit|GTK+ 2.23.0 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-October/msg00128.html #gtk|1287324697|0||0|1
185|GTK+ and friends|GTKtoolkit|RT @bratschegnome: Backported gtk window resize grips to 2.x and posted to ppa:bratsche/gtk for any #ubuntu people who want to use/test it.|1287156390|0||0|1
186|GTK+ and friends|GTKtoolkit|Resize grip in all the GtkWindows now: http://blogs.fedoraproject.org/wp/mclasen/2010/10/09/getting-a-grip/ thanks to @bratschegnome #gtk|1286600672|0||0|1
187|GTK+ and friends|GTKtoolkit|@judsontwit You have some tips for porting here: http://live.gnome.org/PyGObject/IntrospectionPorting No many changes needed #python|1286298188|0||0|0
188|GTK+ and friends|GTKtoolkit|@UstunOzgur take a look here: http://live.gnome.org/PyGTK and here: http://live.gnome.org/PyGObject|1286297979|0||0|0
189|Kristian Rietveld|krietvel|RT @krietvel: Just pushed the last patch that finishes the transition of the OS X backend to the new rendering goodness. #gtk|1286297494|0|GTKtoolkit|0|1
190|GTK+ and friends|GTKtoolkit|ANNOUNCE: gtkmm 2.91.0 (#C++ bindings) released: http://mail.gnome.org/archives/gtkmm-list/2010-October/msg00000.html #gtk|1286128646|0||0|1
191|GTK+ and friends|GTKtoolkit|GTK+ 2.91.0 (unstable) released: http://ur1.ca/1xbzr The rendering cleanup work has landed. Porting guide here: http://ur1.ca/1xbzs #gtk|1286073059|0||0|0
192|GTK+ and friends|GTKtoolkit|Anyone up to the challenge of writing a WebP gdkpixbuf loader?|1285950573|0||0|3
193|Clutter Toolkit|cluttertoolkit|RT @cluttertoolkit: Clutter 1.4.0 - new stable release! grab it while it's hot, on www.clutter-project.org|1285731448|0|GTKtoolkit|0|10
194|GTK+ and friends|GTKtoolkit|We strongly recommend not using PyGTK for new projects and to port existing applications from #PyGTK to #PyGObject #python #gtk|1285721997|0||5|39
195|GTK+ and friends|GTKtoolkit|ANNOUNCE: PyGObject 2.26.0 released:http://mail.gnome.org/archives/python-hackers-list/2010-September/msg00019.html #gtk #python #bindings|1285721968|0||0|0
196|GTK+ and friends|GTKtoolkit|ANNOUNCE: GLib 2.26.0 (STABLE) released: http://mail.gnome.org/archives/gtk-devel-list/2010-September/msg00284.html #glib #gtk|1285721379|0||0|0
197|GTK+ and friends|GTKtoolkit|ANNOUNCE: gtkmm 2.22.0 (STABLE) released: http://mail.gnome.org/archives/gtkmm-list/2010-September/msg00095.html #gtk #c++ #bindings|1285721312|0||0|0
198|GTK+ and friends|GTKtoolkit|PyGTK 2.22.0 released: http://ur1.ca/1sc2n Note that new and existing PyGtk applications are recommended to use PyGObject|1285596826|0||0|0
199|GTK+ and friends|GTKtoolkit|GTK+ 2.22.0 ( STABLE ) released: http://mail.gnome.org/archives/gtk-devel-list/2010-September/msg00263.html #gtk|1285286958|0||2|5
200|GTK+ and friends|GTKtoolkit|Last call for the people interested in attending the #GTK+ hackfest in A Coruña: Please sign up at latest tomorrow! http://ur1.ca/1r2gt|1285255897|0||0|2
201|GTK+ and friends|GTKtoolkit|#GLib status update: GLib 2.25.17 and 2.27.0 released: http://mail.gnome.org/archives/gtk-devel-list/2010-September/msg00232.html #gtk|1285245997|0||0|0
202|GTK+ and friends|GTKtoolkit|Thoughts about #GtkTreeView refactoring: http://mail.gnome.org/archives/gtk-devel-list/2010-September/msg00260.html #gtk|1285245546|0||0|1
203|GTK+ and friends|GTKtoolkit|GTK+ 2.21.8 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-September/msg00204.html #gtk|1284485086|0||0|0
204|GTK+ and friends|GTKtoolkit|New GObject API added: g_object_class_install_properties(), an efficient way to install properties: http://ur1.ca/1mh3s #gobject #glib #gtk|1284484816|0||0|0
205|GTK+ and friends|GTKtoolkit|legacy-free grid container proposed by @havocp: http://mail.gnome.org/archives/gtk-devel-list/2010-September/msg00089.html #gtk|1283965732|0||0|1
206|GTK+ and friends|GTKtoolkit|Minutes of the #GTK team meeting - 2010-09-07: http://mail.gnome.org/archives/gtk-devel-list/2010-September/msg00115.html|1283965715|0||0|0
207|GTK+ and friends|GTKtoolkit|New work to get #DirectFB backend in a good state, thanks Lionel Landwerlin! #gtk http://ur1.ca/1k0hx|1283965546|0||0|0
208|GTK+ and friends|GTKtoolkit|GTK+ 2.21.7 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-August/msg00291.html #gtk|1283198533|0||0|0
209|GTK+ and friends|GTKtoolkit|GObject Introspection status: http://mail.gnome.org/archives/gtk-devel-list/2010-August/msg00254.html #gtk #glib|1282939837|0||1|2
210|GTK+ and friends|GTKtoolkit|GDateTime, the new time & date API just landed in Glib: https://bugzilla.gnome.org/show_bug.cgi?id=50076#c85 #gtk #glib|1282699797|0||0|1
211|GTK+ and friends|GTKtoolkit|CSS-like styling for #GTK+ thanks to Carlos Garnacho: http://blogs.gnome.org/carlosg/2010/08/23/css-like-styling-for-gtk/ #gtk|1282602303|0||0|3
212|GTK+ and friends|GTKtoolkit|RT @migueldeicaza: Gtk+ getting cascading stylesheets: http://blogs.gnome.org/carlosg/2010/08/23/css-like-styling-for-gtk/|1282594548|0||0|1
213|andreasn1|andreasn1|RT @andreasn1: ♺ @hbons: thanks to gtk+ maintainer mclasen we can now ditch icon-naming-utils :)|1282356193|0|GTKtoolkit|0|2
214|GTK+ and friends|GTKtoolkit|GTK+ schedule: glib 2.26 and #gtk+ 2.22 for #GNOME 2.32 (Sep'10). glib 2.28, gtk+ 2.24 and gtk+ 3.0 for Dec'10 http://ur1.ca/16o49|1282256544|0||0|0
215|GTK+ and friends|GTKtoolkit|@thomasvs I saw someone with an N900 in my lift yesterday, had the same thought|1282217985|0||0|0
216|Simón P.|spenap|RT @spenap: GObject Introspection has landed in Grilo! http://bit.ly/9f4DAa #mswl #igalia #pygobject #gobject-introspection|1282166595|0|GTKtoolkit|0|4
217|GTK+ and friends|GTKtoolkit|Minutes of the GTK+ Team Meeting - 2010-08-17: http://mail.gnome.org/archives/gtk-devel-list/2010-August/msg00155.html #gtk|1282094165|0||0|0
218|GTK+ and friends|GTKtoolkit|GTK+ 2.90.6 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-August/msg00146.html #gtk|1282061092|0||0|0
219|Emmanuele Bassi|ebassi|RT @ebassi: http://bit.ly/9gabxR - @cluttertoolkit + gobject-introspection + pygobject. say goodbye to pyclutter!|1282043493|0|GTKtoolkit|0|3
220|GTK+ and friends|GTKtoolkit|GTK+ 2.21.6 (unstable) Relased: http://mail.gnome.org/archives/gtk-devel-list/2010-August/msg00127.html #gtk|1282006494|0||0|0
221|Stormy|storming|RT @storming: Novell is looking for a GNOME developer to work on SUSE Linux. http://linkd.in/bEAUUj|1282001084|0|GTKtoolkit|0|19
222|Tommi Komulainen|tko|RT @tko: I want my libglib-gslist.so and libglib-glist.so .. and some popcorn. maybe just popcorn|**********|0|GTKtoolkit|0|1
223|GTK+ and friends|GTKtoolkit|GLib 2.25.14 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-August/msg00123.html #gtk|**********|0||0|0
224|Emmanuele Bassi|ebassi|RT @ebassi: I'm pretty pleased with the API that landed in json-glib for 0.12|**********|0|GTKtoolkit|0|1
225|GTK+ and friends|GTKtoolkit|The @ebassi implementation to support common licenses in about dialog has been added to #GTK: http://ur1.ca/11u5a|**********|0||0|0
226|GTK+ and friends|GTKtoolkit|Web inspector support lands in #WebKitGtk+ check the screencast demo http://blog.kov.eti.br/?p=118|**********|0||1|0
227|GTK+ and friends|GTKtoolkit|#GTK+ #Python developers are recommended to use the\nGObject-Introspection features available in PyGObject. http://live.gnome.org/PyGObject|**********|0||0|0
228|GTK+ and friends|GTKtoolkit|PyGTK 2.21.0 (unstable) released: http://ur1.ca/11gse . 2.22 will be the last release in the PyGTK series.|**********|0||0|0
229|GTK+ and friends|GTKtoolkit|#GTK+ Hackfest, October 18-22, A Coruña, Spain. http://ur1.ca/11f6u . Add yourself if you are interested in attending http://ur1.ca/11f6v|**********|0||0|4
230|GTK+ and friends|GTKtoolkit|Benjamin Otte's proposal for GTK+ drawing API: gtk_widget_draw(): http://ur1.ca/11f5m #gtk|**********|0||2|0
231|GTK+ and friends|GTKtoolkit|Some drawing APIs have been deprecated in GTK+ 2.22. Start porting your drawing to Cairo! http://ur1.ca/11f4t|1281349855|0||0|0
232|GTK+ and friends|GTKtoolkit|GLib 2.24.2 (stable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-August/msg00057.html #gtk|1281349547|0||0|0
233|GTK+ and friends|GTKtoolkit|glib (unstable) 2.25.13 released: http://mail.gnome.org/archives/gtk-devel-list/2010-August/msg00052.html #gtk|1281201884|0||0|0
234|GTK+ and friends|GTKtoolkit|glib 2.25.12 is here! http://mail.gnome.org/archives/gtk-devel-list/2010-July/msg00052.html There have been many API changes in GDBus.|1280601329|0||0|0
235|Chema Casanova|txenoo|RT @txenoo: Dispoñible a foto de grupo de #guadeces2010 http://www.flickr.com/photos/davizin/4822344878/ gracias a David Cabrero|1280002637|0|GTKtoolkit|0|2
236|Andrea Cimitan|cimi|RT @cimi: Gtk+ 3.0 theming engines... let's start the discussion :)|1279678340|0|GTKtoolkit|0|1
237|Alberto Ruiz|acruiz|RT @acruiz: Marker support in GtkScrollbar http://bit.ly/cKUTeW|1279580539|0|GTKtoolkit|0|2
238|GTK+ and friends|GTKtoolkit|RT @ebassi: today I moved #clutter-gtk to depend on #gtk3; tomorrow w I'll fix the double-events bug; on wednesday I'll rework the API|1279561116|0||0|0
239|Sandy Armstrong|sandyarmstrong|RT @sandyarmstrong: @awafaa gconf is obsolete, fool|1279301600|0|GTKtoolkit|0|1
240|Jonh Wendell|jwendell|RT @jwendell: #vinagre ported to GtkApplication :) !gtk|1279228583|0|GTKtoolkit|0|1
241|Clutter Toolkit|cluttertoolkit|RT @cluttertoolkit: #clutter 1.3.8 is the first snapshot with cally, the accessibility library for clutter apps and toolkits|1278972910|0|GTKtoolkit|0|4
242|GTK+ and friends|GTKtoolkit|GTK+ 2.90.5 released: http://mail.gnome.org/archives/gtk-devel-list/2010-July/msg00021.html #gtk|1278952963|0||0|0
243|GTK+ and friends|GTKtoolkit|Glib 2.25.11 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-July/msg00019.html #gtk|1278952925|0||0|0
244|Johan Dahlin|johandahlin|RT @johandahlin: New blog post: Using LLVM to speed up function invocation in a dynamic language binding http://bit.ly/dA6IjH|1278620689|0|GTKtoolkit|0|4
245|GTK+ and friends|GTKtoolkit|GTK+ bindings for #Falcon announced: http://mail.gnome.org/archives/gtk-list/2010-June/msg00183.html #gtk|1278000421|0||0|0
246|GTK+ and friends|GTKtoolkit|#GSettings / #dconf is ready: http://mail.gnome.org/archives/desktop-devel-list/2010-June/msg00226.html . Please port your modules! #gtk|1277909398|0||0|1
247|GTK+ and friends|GTKtoolkit|More work in height-for-width layout system for GTK+ : http://blogs.gnome.org/tvb/2010/06/30/gtk-learns-height-for-width-episode-ii/ #gtk|1277909313|0||0|0
248|GTK+ and friends|GTKtoolkit|GLib 2.25.10 (unstable) released: http://ur1.ca/0ee7o WARNING: There have been API changes in GDBus. #gtk|1277685452|0||0|1
249|GTK+ and friends|GTKtoolkit|gdk-pixbuf is now a standalone package: http://mail.gnome.org/archives/gtk-devel-list/2010-June/msg00172.html #gtk|1277685365|0||0|1
250|GTK+ and friends|GTKtoolkit|GTK+ 2.90.4 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-June/msg00182.html #gtk|1277685274|0||0|0
251|GTK+ and friends|GTKtoolkit|GTK+ 2.21.3 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-June/msg00183.html #gtk|1277685237|0||0|0
252|GTK+ and friends|GTKtoolkit|We are open to fix API that make the life of #bindings harder but only by addition+rename, or addition+deprecation. File bugs, please #gtk|1277312749|0||0|0
253|GTK+ and friends|GTKtoolkit|Minutes of the GTK+ team IRC meeting - 2010-06-22: http://mail.gnome.org/archives/gtk-devel-list/2010-June/msg00155.html #gtk|1277312445|0||0|0
254|GTK+ and friends|GTKtoolkit|GTK+ team IRC meeting 2010-06-22. In #gtk-devel on irc.gnome.org at 20:00 UTC. Agenda: http://ur1.ca/q6jh #gtk|1277226823|0||0|0
255|Clutter Toolkit|cluttertoolkit|RT @cluttertoolkit: the new #clutter website is now live: http://www.clutter-project.org|1276948072|0|GTKtoolkit|0|5
256|GTK+ and friends|GTKtoolkit|Proposed #GNOME goal: Port your #PyGTK to the new #PyGI bindings http://bit.ly/cvfzO8|1276941335|0||1|2
257|Christian Hergert|hergertme|RT @vwduder: im sad because @ebassi doesn't like my in/out param comments :-)|1276904611|0|GTKtoolkit|0|1
258|Guillaume Mazoyer|gmazoyer|RT @gmazoyer: ♺ @GTKtoolkit: #java bindings version 4.0.16 released: http://article.gmane.org/gmane.comp.gnome.bindings.java/1796 #gtk|1276889421|0|GTKtoolkit|0|1
259|GTK+ and friends|GTKtoolkit|#java bindings version 4.0.16 released: http://article.gmane.org/gmane.comp.gnome.bindings.java/1796 #gtk|1276885917|0||0|0
260|GTK+ and friends|GTKtoolkit|RT @cwiiis: MxIconTheme and MxIcon respect system's icon theme (and changes) now in #mx master :) Made possible by @thosw's XSettings work|1276883019|0||0|0
261|GTK+ and friends|GTKtoolkit|#javascript mailing list just created. Discuss its usage in GObject libraries: GTK+, Glib ... http://ur1.ca/08lwz by @jwendell #gtk|1276842639|0||0|0
262|GTK+ and friends|GTKtoolkit|Note for Win32 users: XP theming is back in 2.90.3 . Please test. #gtk|1276829697|0||0|0
263|GTK+ and friends|GTKtoolkit|GTK+ 2.90.3 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-June/msg00137.html #gtk|1276829633|0||0|0
264|GTK+ and friends|GTKtoolkit|GLib 2.25.9 (unstable) released: http://ur1.ca/08hrl WARNING: API changes in GDBus, GSettings and GApplication #gtk|1276829581|0||0|0
265|scaroo|scaroo|RT @scaroo: #SeedKit does RGBA window with css shadows and stuff : http://dl.dropbox.com/u/5746554/seedkit-does-rgba.png|1276734086|0|GTKtoolkit|0|1
266|scaroo|scaroo|RT @scaroo: Great #SeedKit showcase from @cldx3000 : http://bit.ly/cRDosJ :D|1276734071|0|GTKtoolkit|0|1
267|Lluis Sanchez Gual|slluis|RT @slluis: MonoDevelop 2.4 released! http://monodevelop.com/Download/MonoDevelop_2.4_Released|1276699438|0|GTKtoolkit|0|35
268|GTK+ and friends|GTKtoolkit|RT @bertogg: GNOME Developer Training at GUADEC, with Claudio Saavedra, Fernando Herrera, Dave Neary and me: http://is.gd/cPkpJ|1276687240|0||0|0
269|SparkleShare|SparkleShare|RT @sparkleshare: Let's start sparkling!|1276619215|0|GTKtoolkit|0|7
270|Haakon Sporsheim|haaspors|RT @haakonsporsheim: I built my first app for #android today using jni and #glib :P Sweet :)|1276472258|0|GTKtoolkit|0|4
271|GTK+ and friends|GTKtoolkit|Converting libraries and plugins to use GTK+3: http://mail.gnome.org/archives/desktop-devel-list/2010-June/msg00142.html #gtk|1276390360|0||1|0
272|GTK+ and friends|GTKtoolkit|Call to GNOME maintainers: #GNOME 2.31.4 to ship GTK+ 2.90: http://bit.ly/bnuk3e #gtk|1276390311|0||0|0
273|GTK+ and friends|GTKtoolkit|API changes in GLib master: http://mail.gnome.org/archives/gtk-devel-list/2010-June/msg00079.html #gtk|1276390197|0||1|0
274|GTK+ and friends|GTKtoolkit|GLib 2.25.8 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-June/msg00036.html #gtk|1276390151|0||0|0
275|Johan Dahlin|johandahlin|RT @johandahlin: is adding introspection support for GstMiniObject and other weird instantitiable GTypes. Still left: gjs/pygi support.|1276384704|0|GTKtoolkit|0|3
276|Clutter Toolkit|cluttertoolkit|RT @cluttertoolkit: So, yes: we dropped the copyright waiver on Clutter and Cogl. Contributions welcome!|1276281694|0|GTKtoolkit|0|12
277|GTK+ and friends|GTKtoolkit|ANNOUNCE: gtkmm (C++ GTK+ bindings) 2.90 (unstable) released: http://ur1.ca/06nhn #gtk|1276182909|0||0|0
278|Juanje Ojeda |juanjeojeda|RT @juanjeojeda: Progress on Gtk+ multitouch support: http://bit.ly/amIUiL #gtk #multitouch /via @garnacho|1276158728|0|GTKtoolkit|0|2
279|GTK+ and friends|GTKtoolkit|GTK+ 2.90.2 (unstable) released: http://ur1.ca/06k6o Feedback about GtkApplication apreciated #gtk|1276141907|0||0|0
280|GTK+ and friends|GTKtoolkit|Minutes of the GTK+ team IRC meeting - 2010-06-08: http://mail.gnome.org/archives/gtk-devel-list/2010-June/msg00044.html #gtk|1276040191|0||0|0
281|GTK+ and friends|GTKtoolkit|RT @bratschegnome: @federicomena http://mzl.la/9PoFhD is nice I used to have CSD whr you can drag gtk+ from anywr in a window|1276038852|0||0|0
282|GTK+ and friends|GTKtoolkit|GTK+ team IRC meeting 2010-06-08.In #gtk-devel on irc.gnome.org at 20:00 UTC.Agenda: http://ur1.ca/q6jh Everyone is invited to attend|1276010278|0||0|0
283|GTK+ and friends|GTKtoolkit|ANNOUNCE: glibmm (C++ Glib bindings) 2.25.1 (unstable) released: http://ur1.ca/067ff #gtk|1276007921|0||0|0
284|GTK+ and friends|GTKtoolkit|GLib 2.25.8 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-June/msg00036.html #gtk|1276003038|0||0|1
285|GTK+ and friends|GTKtoolkit|New version of #pygi (the new #python bindings based in #GObjectIntrospection) released: http://ur1.ca/0623c|1275945620|0||0|3
286|GTK+ and friends|GTKtoolkit|RT @ebassi: aaaand GBinding (a libexo-like binding between object properties) is mostly done: http://ur1.ca/05fz1 #gtk #glib|1275653238|0||1|0
287|GTK+ and friends|GTKtoolkit|RT @ebassi: plus, I have a GIO branch with GController and friends|1275653044|0||0|0
288|GTK+ and friends|GTKtoolkit|RT @ebassi submitted my patch for creating a GObjectController and get bulk notification #gtk|1275653008|0||0|0
289|GTK+ and friends|GTKtoolkit|GTK+ 2.21.1 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00157.html #gtk|1275271768|0||0|0
290|GTK+ and friends|GTKtoolkit|#dtrace and #systemtap support added to #Glib. Enjoy! https://bugzilla.gnome.org/show_bug.cgi?id=606044 #gtk|1275056183|0||0|2
291|GTK+ and friends|GTKtoolkit|GTK+ 2.90.1 (unstable) released: http://ur1.ca/03hbv . Multiple input device support, flippable widgets and more ...|1274845319|0||1|0
292|GTK+ and friends|GTKtoolkit|Minutes of the GTK+ team IRC meeting - 2010-05-25: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00147.html #gtk #meeting|1274826674|0||0|0
293|GTK+ and friends|GTKtoolkit|[REMINDER] GTK+ team IRC meeting 2010-05-25 at 20:00 UTC. Join us in #gtk-devel on irc.gnome.org. Agenda: http://ur1.ca/q6jh #gtk #meeting|1274801128|0||0|1
294|GTK+ and friends|GTKtoolkit|Gtk2Hs 0.11.0 (Haskell bindings) released: http://haskell.org/gtk2hs/archives/2010/05/25/gtk2hs-0110-released/ #gtk #haskell|1274800929|0||0|0
295|GTK+ and friends|GTKtoolkit|dconf 0.3.1 released: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00145.html #gtk|1274800819|0||0|0
296|GTK+ and friends|GTKtoolkit|GLib 2.25.7 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00144.html #gtk|1274800611|0||0|0
297|GTK+ and friends|GTKtoolkit|XI2 @garnacho 's branch ready for review (xi2-for-master): http://mail.gnome.org/archives/gtk-devel-list/2010-May/thread.html #gtk|1274472793|0||0|0
298|GTK+ and friends|GTKtoolkit|ANNOUNCE: gtk-doc 1.15 released: http://mail.gnome.org/archives/gtk-doc-list/2010-May/msg00000.html #gtk|1274446357|0||0|0
299|GTK+ and friends|GTKtoolkit|Ryan Lortie (@desrt) just released #dconf 0.3: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00128.html #gtk|1274311034|0||0|0
300|GTK+ and friends|GTKtoolkit|GLib 2.25.6 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00127.html #gtk|1274310863|0||0|0
301|GTK+ and friends|GTKtoolkit|GLib 2.25.6 (unstable) released:|1274310818|0||0|0
302|GTK+ and friends|GTKtoolkit|GLib 2.25.5 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00078.html #gtk|1273891783|0||0|0
303|GTK+ and friends|GTKtoolkit|Glade 3.7.1 released with lot of improvements: http://ur1.ca/011bc Thanks to #Openismus who helped sponsor this release #gtk|1273885948|0||0|0
304|GTK+ and friends|GTKtoolkit|GDBus merged in Glib master http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00066.html #gtk|1273837079|0||0|1
305|GTK+ and friends|GTKtoolkit|Minutes of the GTK+ team IRC meeting - 2010-05-11 : http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00047.html #gtk|1273636581|0||0|0
306|GTK+ and friends|GTKtoolkit|[REMINDER] GTK+ team IRC meeting - 2010-05-11 at 20:00 UTC. Join us in #gtk-devel on irc.gnome.org. Agenda: http://ur1.ca/q6jh #gtk #meeting|1273606386|0||0|0
307|GTK+ and friends|GTKtoolkit|GTK+ 2.90.0 (unstable) released. This is the first development release leading toward 3.0. http://ur1.ca/006p2 #gtk #gtk3|1273553873|0||0|5
308|GTK+ and friends|GTKtoolkit|Changes in GTK+ master that affect third parties: http://mail.gnome.org/archives/devel-announce-list/2010-May/msg00001.html #gtk #gtk3|1273531549|0||0|0
309|GTK+ and friends|GTKtoolkit|Have dark themes is more easy now thanks to Bastian Nocera work: http://bit.ly/dBJzgn #gtk|1273531264|0||0|2
310|GTK+ and friends|GTKtoolkit|GTK+ 2.21.0 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00026.html #gtk|1273285878|0||0|0
311|GTK+ and friends|GTKtoolkit|Minutes of the GTK+ IRC team meeting - 2010-05-04: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00010.html #gtk|1273024620|0||0|0
312|GTK+ and friends|GTKtoolkit|GTK+ 2.20.1 (stable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00004.html #gtk|1272983158|0||0|0
313|GTK+ and friends|GTKtoolkit|GLib 2.24.1 (stable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-May/msg00005.html #gtk|1272983138|0||0|0
314|GTK+ and friends|GTKtoolkit|Next GTK+ team meeting: 2010-05-04 at 20:00 UTC. More info and agenda: http://ur1.ca/q6jh . As always, everyone is invited to attend. #gtk|1272848781|0||1|0
315|GTK+ and friends|GTKtoolkit|#Perl bindings: Gtk2 1.230 (unstable) available: http://mail.gnome.org/archives/gtk-perl-list/2010-April/msg00120.html #gtk|1272341271|0||0|0
316|Emmanuele Bassi|ebassi|RT @ebassi: for the first time in ages I was able to work a bit on #gtkperl and add missing 2.16 and 2.18 wrappers|1272139155|0|GTKtoolkit|0|1
317|GTK+ and friends|GTKtoolkit|GLib 2.25.3 (unstable) released with more #GSettings fixes: http://mail.gnome.org/archives/gtk-devel-list/2010-April/msg00090.html #gtk|1272118354|0||0|1
318|GTK+ and friends|GTKtoolkit|GLib 2.25.2 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-April/msg00079.html|1272028911|0||0|0
319|GTK+ and friends|GTKtoolkit|GTK+ Learns height-for-width geometry. Thanks Tristan Van Berkom and #Openismus for sponsoring him! http://ur1.ca/wiz3 #gtk|1271969484|0||1|2
320|GTK+ and friends|GTKtoolkit|#GSettings porting guide available: http://ur1.ca/w2xl . Feedback welcomed!: http://ur1.ca/w2xn #gtk|1271777672|0||4|4
321|GTK+ and friends|GTKtoolkit|Major change in Glib 2.25.0: #GSettings framework has been merged. This provides the API to replace #GConf.|1271715090|0||0|0
322|GTK+ and friends|GTKtoolkit|GLib 2.25.0 (unstable) released: http://mail.gnome.org/archives/gtk-devel-list/2010-April/msg00066.html #gtk|1271714608|0||0|1
323|GTK+ and friends|GTKtoolkit|GSettings status update by Matthias Clasen: http://blogs.fedoraproject.org/wp/mclasen/2010/04/17/gsettings/ #gtk #gsettingshackfest|1271519572|0||0|0
324|GTK+ and friends|GTKtoolkit|GNOME Python Hackfest: Day 1 (by John (J5) Palmieri): http://www.j5live.com/2010/04/14/gnome-python-hackfest-day-1/ #python #pythonhackfest|1271291950|0||0|1
325|GTK+ and friends|GTKtoolkit|Colin Walters: PyGTK, PyGI and PyGTK-on-PyGI #python #pythonhackfest http://ur1.ca/v5kw|1271291792|0||0|0
326|GTK+ and friends|GTKtoolkit|Python Hackfest started.Ttwo concrete goals: porting PyGObject to #Python 3.x and giving a push to PyGI. http://ur1.ca/v5jc|1271291075|0||0|0
327|GTK+ and friends|GTKtoolkit|GSettings Hackfest: Day 1 (by Vincent UNTZ) http://www.vuntz.net/journal/post/2010/04/13/GSettings-Hackfest:-Day-1 #gtk|1271290824|0||0|0
328|GTK+ and friends|GTKtoolkit|GSettings Hackfest started. Thanks to #Novell for sponsoring.Also to #RedHat, #Codethink and #Lanedo for sending people! http://ur1.ca/v5i4|1271290481|0||1|0
329|GTK+ and friends|GTKtoolkit|Xan Lopez from #Igalia attends the WebKit Contribution Meeting at the Apple HQ in Cupertino http://bit.ly/bHCqcC|1271247431|0||0|2
330|GTK+ and friends|GTKtoolkit|Kristian Rietveld advances the #GTK+ Quartz/Mac OS X backend http://bit.ly/cJzV2o|1271247006|0||2|0
331|GTK+ and friends|GTKtoolkit|#Openismus sponsors Tristan Van Berkom to complete the work on #GTK+ Natural Layout http://bit.ly/9FD3JC|1271246851|0||0|1
332|GTK+ and friends|GTKtoolkit|RT @bilboed Having trouble reading GObject or GStreamer code ? Here's a small step-by-step rundown : http://is.gd/bk7mD|1270738689|0||0|1
333|GTK+ and friends|GTKtoolkit|Colin Walters shares his thoughts about the new #GTK+ application class and its relationship with #GNOME 3 http://bit.ly/cvcHIG|1270482931|0||0|0
334|Lucas Rocha|lucasratmundo|RT @lucasratmundo: GNOME Shell has been officially proposed as a GNOME module: http://bit.ly/d1yKE2|1269993362|0|GTKtoolkit|0|3
335|Johan Dahlin|johandahlin|RT @johandahlin: New blog post http://blogs.gnome.org/johan/2010/03/30/bridging-the-development-gap-between-desktop-and-web/|1269993352|0|GTKtoolkit|0|1
336|GTK+ and friends|GTKtoolkit|New Glib STABLE release: 2.24 http://mail.gnome.org/archives/gtk-devel-list/2010-March/msg00149.html #gtk|1269892314|0||0|0
337|GTK+ and friends|GTKtoolkit|Help the GNOME Foundation to hire a sysadmin for GNOME! http://www.gnome.org/friends|1269658993|0||0|1
338|GTK+ and friends|GTKtoolkit|Our friends from #Openismus sponsor #Glade 3 improvements http://bit.ly/9GqLDl|1269656334|0||0|0
339|GTK+ and friends|GTKtoolkit|♺ @johandahlin: just commited the last remaining piece for cairo support in #gjs. World domination here we come|1269646821|0||0|0
340|FedericoMenaQuintero|federicomena|RT @federicomena: Yay, the patch for glade is done - https://bugzilla.gnome.org/show_bug.cgi?id=594231|1269616128|0|GTKtoolkit|0|1
341|Jorge Castro|castrojo|RT @castrojo: Become a Friend of GNOME: http://wp.me/poAPi-da|1269616083|0|GTKtoolkit|0|2
342|Johan Dahlin|johandahlin|RT @johandahlin: just commited the last remaining piece for cairo support in #gjs. World domination here we come|1269616001|0|GTKtoolkit|0|2
343|Jono Bacon|jonobacon|RT @jonobacon: New Acire and Python Snippets website! http://is.gd/aZGnF - still a work in progress, working on it as we speak! #pythons ...|1269615964|0|GTKtoolkit|0|5
344|GTK+ and friends|GTKtoolkit|Python Snippets: project to gather an archive of simple Python (with GTK+) examples http://ur1.ca/rru1 !gtk @jonobacon|1269555086|0||0|2
345|GTK+ and friends|GTKtoolkit|#yorbafoundation is hiring #Vala/#Gtk+ developers and summer interns in California to create multimedia apps http://yorba.org/jobs|1269479714|0||0|1
346|GTK+ and friends|GTKtoolkit|♺ @ploum: now that I have to use Qt and Qt documentation, I've only one word : #gtk rocks ! ( yeah for #gnome people)|1269441827|0||0|2
347|GTK+ and friends|GTKtoolkit|Minutes of the GTK+ Team Meeting: http://mail.gnome.org/archives/gtk-devel-list/2010-March/msg00134.html #gtk|1269391167|0||0|0
348|GTK+ and friends|GTKtoolkit|New GTK+ STABLE release: 2.20 http://mail.gnome.org/archives/gtk-devel-list/2010-March/msg00132.html #gtk|1269374138|0||1|1
349|GTK+ and friends|GTKtoolkit|Reminder: GTK+ meeting today at 20:00 UTC. Where: #gtk-devel on irc.gnome.orgAgenda: http://live.gnome.org/GTK+/Meetings|1269358583|0||0|0
350|GTK+ and friends|GTKtoolkit|GLib 2.23.6 (development branch) released: http://mail.gnome.org/archives/gtk-devel-list/2010-March/msg00131.html #gtk|1269264623|0||0|0
351|GNOME|gnome|RT @gnome: RT @rubenv GNOME was accepted for Google Summer of Code 2010! Looking for an IT student job that earns a lot? http://bit.ly/c ...|1269106193|0|GTKtoolkit|0|5
352|FedericoMenaQuintero|federicomena|RT @federicomena: Untested code is broken code, even if it compiles. #yay #me|1269106077|0|GTKtoolkit|0|1
353|Summer of Code|gsoc|RT @gsoc: Mentor organizations for #GSoC have been announced! http://bit.ly/bVMPWe|1268956782|0|GTKtoolkit|0|31
354|Clutter Toolkit|cluttertoolkit|RT @cluttertoolkit: just released clutter-gtk 0.10.4 - depending on clutter 1.2 and gtk+ 2.19|1268956726|0|GTKtoolkit|0|1
355|GTK+ and friends|GTKtoolkit|Support for Class private data will be available in Glib 2.24: https://bugzilla.gnome.org/show_bug.cgi?id=521707|1268848373|0||0|2
356|GTK+ and friends|GTKtoolkit|GTK+ team IRC meeting: March 23, at 20:00. http://ur1.ca/q6jg . Agenda: http://ur1.ca/q6jh #gtk|1268845861|0||0|1
357|GTK+ and friends|GTKtoolkit|GTK+ 2.18.9 (stable branch) released: http://mail.gnome.org/archives/gtk-devel-list/2010-March/msg00115.html #gtk|1268844918|0||0|0
358|GTK+ and friends|GTKtoolkit|GLib 2.22.5 released: http://mail.gnome.org/archives/gtk-devel-list/2010-March/thread.html #gtk|1268748183|0||0|0
359|GTK+ and friends|GTKtoolkit|GTK+ 2.18.8 released: http://mail.gnome.org/archives/gtk-devel-list/2010-March/msg00078.html #gtk|1268700945|0||0|0
360|GTK+ and friends|GTKtoolkit|@chrisblizzard There's a .zip bundle indeed http://bit.ly/9ZkQCM|1268698714|0||0|0
361|Alberto Ruiz|acruiz|RT @acruiz: libmodel and GTK+ from Codethink Labs! http://aruiz.synaptia.net/siliconisland/2010/03/libmodel-and-gtk-from-codethink-labs.html|1268698629|0|GTKtoolkit|0|3
362|GTK+ and friends|GTKtoolkit|New version of the User Interface Designer #Glade released: 3.7.0 http://ur1.ca/phww #gtk|1268458482|0||0|0
363|GTK+ and friends|GTKtoolkit|GTK+ 2.19.7 released: http://mail.gnome.org/archives/gtk-devel-list/2010-March/msg00044.html #gtk|1268174602|0||0|0
364|GTK+ and friends|GTKtoolkit|#GTK + future idea: automatic composite widgets using #GtkBuilder under the hood http://ur1.ca/oxly|1268157762|0||0|0
365|GTK+ and friends|GTKtoolkit|GLib 2.23.5 is released, congrats to @desrt for his first release http://bit.ly/a3th6S|1268070995|0||0|1
366|GTK+ and friends|GTKtoolkit|The first #PyGTK hackfest ever has been announced, 3.0 and Introspection are the major themes http://bit.ly/9Bd31g|1267875279|0||1|1
367|GTK+ and friends|GTKtoolkit|Kristian Rietveld gives an update of the GTK+/Quartz MacOSX native port http://bit.ly/cZ84VN|1267832356|0||2|0
368|Jono Bacon|jonobacon|RT @jonobacon: Merged in more python-snippets: desktop widget, drag and open in PyGTK, GStreamer video playback, and a bunch of fixes! h ...|1267831925|0|GTKtoolkit|0|3
369|GTK+ and friends|GTKtoolkit|#Openismus is looking for C/C++ GTK+/Qt trainees http://bit.ly/c16WEp|1267831840|0||0|0
370|Clutter Toolkit|cluttertoolkit|RT @cluttertoolkit: clutter 1.2.0 - first stable release, with lots of new API - http://bit.ly/ckdS6R|1267561885|0|GTKtoolkit|0|9
371|GTK+ and friends|GTKtoolkit|#Lanedo is hiring GTK+/GNOME hackers! http://bit.ly/d6fTWQ|1267560392|0||0|1
372|GTK+ and friends|GTKtoolkit|You can help to make a difference too, help the #GTK+ maintainers to improve the documetnation infrastructure! http://bit.ly/dmJifE|1267538283|0||0|1
373|GTK+ and friends|GTKtoolkit|Designers bring back excitement around the #GNOME project http://bit.ly/9Zcx8c|1267202696|0||0|1
374|GTK+ and friends|GTKtoolkit|#webkit #gtk gets ARGB support, allowing it to set a transparent background! http://bit.ly/cBeouj|1267146282|0||0|2
375|GTK+ and friends|GTKtoolkit|Follow GNOME TV on Vimeo http://is.gd/96PlT|1267050829|0||0|1
376|GTK+ and friends|GTKtoolkit|Extensive article on the state of #WebKitGtk http://is.gd/95En2|1267031517|0||0|0
377|GTK+ and friends|GTKtoolkit|@ploum Are you hitting !PyGTK or !GTK+ bugs? Are they already reported upstream?|1267029810|0||0|0
378|GTK+ and friends|GTKtoolkit|#GNUStep gets #GTK+ theming http://is.gd/95vHl more at http://is.gd/95wt8|1267029498|0||0|1
379|GTK+ and friends|GTKtoolkit|GTK+ 2.19.6 released: http://mail.gnome.org/archives/gtk-devel-list/2010-February/msg00050.html #gtk|1266966985|0||0|0
380|GTK+ and friends|GTKtoolkit|Did you know that #GTK+ is the official toolkit for the #LiMo software stack? http://bit.ly/cuEdHx|1266925406|0||0|0
381|GTK+ and friends|GTKtoolkit|@lmedinas publishes a #javascript #example on how to put a status icon with #Gtk+ http://bit.ly/9py1uC Thanks a lot Luis!|1266886644|0||1|1
382|GTK+ and friends|GTKtoolkit|♺ @ebassi: I should really finish up the GDom API as well|1266880653|0||0|0
383|GTK+ and friends|GTKtoolkit|♺ @ebassi: I hope to work on this for the next GIO release, and the GTK+ side for 3.0|1266880641|0||0|0
384|GTK+ and friends|GTKtoolkit|♺ @ebassi: just updated the ApplicationClass design wiki page with the stuff @Cwiiis did for Mx - http://bit.ly/cfAOJk|1266880559|0||0|0
385|GTK+ and friends|GTKtoolkit|#GTK+ Kick Start tutorial for #Vala http://www.vimeo.com/9617309 OGG: http://bit.ly/czegmp|1266874471|0||0|1
386|GTK+ and friends|GTKtoolkit|@migheldeicaza shows off #monodevelop on #macosx deploying and debugging #gtk sharp apps on a #MeeGo device http://bit.ly/9XR0Pg|1266874171|0||1|1
387|GTK+ and friends|GTKtoolkit|#GTK+ is the first toolkit to expose the #Xorg multitouch stack through #XI2 http://bit.ly/9tniKu - Nice work @garnacho!|1266863259|0||0|1
388|GTK+ and friends|GTKtoolkit|This is the official GTK+ first micropost!|1266856657|0||0|1
