<?xml version="1.0" encoding="UTF-8"?>
<gresources>
  <gresource prefix="/org/gtk/WidgetFactory4">
    <file preprocess="xml-stripblanks">widget-factory.ui</file>
  </gresource>
  <gresource prefix="/org/gtk/WidgetFactory4">
    <file>widget-factory.css</file>
  </gresource>
  <gresource prefix="/org/gtk/WidgetFactory4/gtk">
    <file preprocess="xml-stripblanks">help-overlay.ui</file>
  </gresource>
  <gresource prefix="/org/gtk/WidgetFactory4">
    <file>icons/scalable/actions/call-start-symbolic.svg</file>
    <file>icons/scalable/actions/call-stop-symbolic.svg</file>
    <file>icons/scalable/actions/document-new-symbolic.svg</file>
    <file>icons/scalable/actions/format-indent-less-symbolic-rtl.svg</file>
    <file>icons/scalable/actions/format-indent-less-symbolic.svg</file>
    <file>icons/scalable/actions/format-indent-more-symbolic-rtl.svg</file>
    <file>icons/scalable/actions/format-indent-more-symbolic.svg</file>
    <file>icons/scalable/actions/format-justify-center-symbolic.svg</file>
    <file>icons/scalable/actions/format-justify-fill-symbolic.svg</file>
    <file>icons/scalable/actions/format-justify-left-symbolic.svg</file>
    <file>icons/scalable/actions/format-justify-right-symbolic.svg</file>
    <file>icons/scalable/actions/insert-link-symbolic.svg</file>
    <file>icons/scalable/actions/send-to-symbolic.svg</file>
    <file>icons/scalable/actions/star-new-symbolic.svg</file>
    <file>icons/scalable/actions/view-continuous-symbolic.svg</file>
    <file>icons/scalable/actions/view-dual-symbolic.svg</file>
    <file>icons/scalable/actions/view-paged-symbolic.svg</file>
    <file>icons/scalable/actions/view-fullscreen-symbolic.svg</file>
    <file>icons/scalable/actions/zoom-in-symbolic.svg</file>
    <file>icons/scalable/actions/zoom-original-symbolic.svg</file>
    <file>icons/scalable/actions/zoom-out-symbolic.svg</file>

    <file>icons/scalable/devices/audio-headphones-symbolic.svg</file>
    <file>icons/scalable/devices/audio-speakers-symbolic.svg</file>
    <file>icons/scalable/devices/audio-input-microphone-symbolic.svg</file>
    <file>icons/scalable/devices/camera-photo-symbolic.svg</file>
    <file>icons/scalable/devices/camera-web-symbolic.svg</file>
    <file>icons/scalable/devices/drive-optical-symbolic.svg</file>
    <file>icons/scalable/devices/network-wired-symbolic.svg</file>
    <file>icons/scalable/devices/network-wireless-symbolic.svg</file>
    <file>icons/scalable/devices/phone-symbolic.svg</file>

    <file>icons/scalable/status/appointment-soon-symbolic.svg</file>
    <file>icons/scalable/status/weather-clear-night-symbolic.svg</file>
    <file>icons/scalable/status/weather-few-clouds-night-symbolic.svg</file>
    <file>icons/scalable/status/weather-fog-symbolic.svg</file>
    <file>icons/scalable/status/weather-overcast-symbolic.svg</file>
    <file>icons/scalable/status/weather-severe-alert-symbolic.svg</file>
    <file>icons/scalable/status/weather-showers-symbolic.svg</file>
    <file>icons/scalable/status/weather-snow-symbolic.svg</file>

    <file alias='icons/scalable/apps/org.gtk.WidgetFactory4.svg'>data/scalable/apps/org.gtk.WidgetFactory4.svg</file>

  </gresource>
  <gresource prefix="/org/gtk/WidgetFactory4">
    <file>gtk-logo.webm</file>
    <file>sunset.jpg</file>
    <file>portland-rose.jpg</file>
    <file>nyc.jpg</file>
    <file>beach.jpg</file>
  </gresource>
</gresources>
