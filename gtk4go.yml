--- 
GENERATOR: 
  PackageName: gtk4go
  PackageDescription: "GTK4 bindings for Go."
  PackageLicense: "GPLv3"
  FlagGroups:
    - 
  SysIncludes:
    -

PARSER:
  Arch: amd64
  SourcesPaths:
    - 
  IncludePaths:
    -
  Defines:

TRANSLATOR: 
  ConstRules: 
    defines: expand
    enum: expand
  TypeTips:
    function:
      - {target: ., self: plain, tips: [plain,plain,plain,plain,plain,plain,plain,plain,plain]}
  Rules: 
    global:
      - {action: accept, from: "^GL"}
      - {action: replace, from: "^GL"}
    function:
      - {action: accept, from: ^gl}
      - {action: replace, from: ^gl}
      - {transform: export}
    type:
      - {action: replace, from: "_t$"}
      - {transform: export}
      - {load: snakecase}
    const:
      - {action: replace, from: "^_"}
    private:
      - {transform: unexport}
      - {load: snakecase}
    post-global:
      - 
