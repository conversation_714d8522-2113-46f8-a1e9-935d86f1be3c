---
GENERATOR:
  PackageName: gtk4go
  PackageDescription: "Package gtk4go provides Go bindings for GTK4."
  PackageLicense: "THE AUTOGENERATED LICENSE. ALL THE RIGHTS ARE RESERVED BY ROBOTS."
  FlagGroups:
    - {name: CFLAG<PERSON>, flags: ["pkg-config gtk4"]}
    - {name: LDFLAGS, flags: ["pkg-config gtk4"]}
  SysIncludes:
    - gtk/gtk.h

PARSER:
  Arch: amd64
  SourcesPaths:
    - gtk4_simple.h
  IncludePaths:
    - /usr/include/glib-2.0
    - /usr/lib/glib-2.0/include
    - /usr/include/cairo
  Defines:
    __GTK_H_INSIDE__: null
    __GDK_H_INSIDE__: null
    __GSK_H_INSIDE__: null

TRANSLATOR:
  ConstRules:
    defines: expand
    enum: expand
  TypeTips:
    type:
      - {target: ^gboolean$, self: plain}
      - {target: ^gchar$, self: plain}
      - {target: ^guchar$, self: plain}
      - {target: ^gint$, self: plain}
      - {target: ^guint$, self: plain}
      - {target: ^glong$, self: plain}
      - {target: ^gulong$, self: plain}
      - {target: ^gfloat$, self: plain}
      - {target: ^gdouble$, self: plain}
      - {target: ^gsize$, self: plain}
      - {target: ^gssize$, self: plain}
  PtrTips:
    function:
      - {target: ".", tips: [sref,sref,sref,sref,sref]} # ref by default for all
  Rules:
    global:
      - {action: accept, from: "^GTK"}
      - {action: accept, from: "^Gtk"}
      - {action: accept, from: "^gtk"}
      - {action: accept, from: "^GDK"}
      - {action: accept, from: "^Gdk"}
      - {action: accept, from: "^gdk"}
      - {action: accept, from: "^GSK"}
      - {action: accept, from: "^Gsk"}
      - {action: accept, from: "^gsk"}
      - {action: replace, from: "^gtk_", to: ""}
      - {action: replace, from: "^GTK_", to: ""}
      - {action: replace, from: "^Gtk", to: ""}
      - {action: replace, from: "^gdk_", to: "Gdk"}
      - {action: replace, from: "^GDK_", to: "Gdk"}
      - {action: replace, from: "^Gdk", to: "Gdk"}
      - {action: replace, from: "^gsk_", to: "Gsk"}
      - {action: replace, from: "^GSK_", to: "Gsk"}
      - {action: replace, from: "^Gsk", to: "Gsk"}
    function:
      - {action: accept, from: "^gtk_"}
      - {action: accept, from: "^gdk_"}
      - {action: accept, from: "^gsk_"}
      - {action: replace, from: "^gtk_"}
      - {action: replace, from: "^gdk_", to: "Gdk"}
      - {action: replace, from: "^gsk_", to: "Gsk"}
      - {transform: export}
    type:
      - {action: accept, from: "^Gtk"}
      - {action: accept, from: "^Gdk"}
      - {action: accept, from: "^Gsk"}
      - {action: accept, from: "^G"}
      - {action: replace, from: "_t$"}
      - {action: replace, from: "^Gtk"}
      - {action: replace, from: "^Gdk", to: "Gdk"}
      - {action: replace, from: "^Gsk", to: "Gsk"}
      - {transform: export}
    const:
      - {action: accept, from: "^GTK_"}
      - {action: accept, from: "^GDK_"}
      - {action: accept, from: "^GSK_"}
      - {action: replace, from: "^GTK_"}
      - {action: replace, from: "^GDK_", to: "Gdk"}
      - {action: replace, from: "^GSK_", to: "Gsk"}
      - {transform: lower}
    private:
      - {transform: unexport}
    post-global:
      - {transform: export}
      - {load: snakecase}
