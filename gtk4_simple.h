#ifndef GTK4_SIMPLE_H
#define GTK4_SIMPLE_H

// Basic C types
typedef int gboolean;
typedef char gchar;
typedef unsigned char guchar;
typedef int gint;
typedef unsigned int guint;
typedef long glong;
typedef unsigned long gulong;
typedef float gfloat;
typedef double gdouble;
typedef unsigned long gsize;
typedef long gssize;
typedef void* gpointer;
typedef const void* gconstpointer;

// Basic GTK4 forward declarations
typedef struct _GtkWidget GtkWidget;
typedef struct _GtkWindow GtkWindow;
typedef struct _GtkApplication GtkApplication;
typedef struct _GtkButton GtkButton;
typedef struct _GtkLabel GtkLabel;
typedef struct _GtkBox GtkBox;

// Basic GDK types
typedef struct _GdkDisplay GdkDisplay;
typedef struct _GdkSurface GdkSurface;
typedef struct _GdkDevice GdkDevice;
typedef struct _GdkEvent GdkEvent;

// Basic GSK types
typedef struct _GskRenderer GskRenderer;
typedef struct _GskRenderNode GskRenderNode;

// Basic enums
typedef enum {
  GTK_ORIENTATION_HORIZONTAL,
  GTK_ORIENTATION_VERTICAL
} GtkOrientation;

typedef enum {
  GTK_ALIGN_FILL,
  GTK_ALIGN_START,
  GTK_ALIGN_END,
  GTK_ALIGN_CENTER,
  GTK_ALIGN_BASELINE
} GtkAlign;

// Basic function declarations
GtkApplication* gtk_application_new(const gchar *application_id, int flags);
int gtk_application_run(GtkApplication *app, int argc, char **argv);
GtkWidget* gtk_application_window_new(GtkApplication *app);
GtkWidget* gtk_window_new(void);
void gtk_window_set_title(GtkWindow *window, const gchar *title);
void gtk_window_set_default_size(GtkWindow *window, gint width, gint height);
void gtk_window_present(GtkWindow *window);
GtkWidget* gtk_button_new_with_label(const gchar *label);
GtkWidget* gtk_label_new(const gchar *str);
GtkWidget* gtk_box_new(GtkOrientation orientation, gint spacing);
void gtk_box_append(GtkBox *box, GtkWidget *child);
void gtk_window_set_child(GtkWindow *window, GtkWidget *child);
void gtk_widget_show(GtkWidget *widget);

// Signal connection
typedef void (*GCallback)(void);
gulong g_signal_connect(gpointer instance, const gchar *detailed_signal, GCallback c_handler, gpointer data);

// Basic initialization
void gtk_init(void);
gboolean gtk_init_check(void);

#endif // GTK4_SIMPLE_H
