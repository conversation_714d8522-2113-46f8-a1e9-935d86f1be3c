package main

import (
	"fmt"
	"os"

	"gtk4go"
)

func main() {
	// GTK4 başlatma
	gtk4go.Init()

	// Uygulama oluştur
	app := gtk4go.ApplicationNew("com.example.gtk4go", 0)
	if app == nil {
		fmt.Println("Uygulama oluşturulamadı")
		os.Exit(1)
	}

	// Ana pencere oluştur
	window := gtk4go.WindowNew()
	if window == nil {
		fmt.Println("Pencere oluşturulamadı")
		os.Exit(1)
	}

	// Pencere başlığını ayarla
	gtk4go.WindowSetTitle((*gtk4go.Window)(window), "GTK4 Go Test")

	// Pencere boyutunu ayarla
	gtk4go.WindowSetDefaultSize((*gtk4go.Window)(window), 400, 300)

	// Buton oluştur
	button := gtk4go.ButtonNewWithLabel("Merhaba GTK4!")
	if button == nil {
		fmt.Println("Buton oluşturulamadı")
		os.Exit(1)
	}

	// Butonu pencereye ekle
	gtk4go.WindowSetChild((*gtk4go.Window)(window), (*gtk4go.Widget)(button))

	// Pencereyi göster
	gtk4go.WindowPresent((*gtk4go.Window)(window))

	fmt.Println("GTK4 Go binding'leri başarıyla test edildi!")
	fmt.Println("Pencere oluşturuldu ve gösterildi.")
}
